<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Привязка скиллов к мобам - Админ панель</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        {{-- Заголовок страницы --}}
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">🔗 Привязка скиллов к мобам</h1>
                    <p class="text-[#998d66]">Назначение скиллов конкретным мобам в игре</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.mob-skills.index') }}" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к списку скиллов
                    </a>
                </div>
            </div>
        </div>

        {{-- Выбор моба --}}
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-[#3b3629]">
                <h2 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                    <span class="text-2xl mr-3">👹</span>
                    Выбор моба
                </h2>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('admin.mob-skills.assign') }}" class="flex items-end space-x-4">
                    <div class="flex-1">
                        <label for="mob_id" class="block text-sm font-medium text-[#c1a96e] mb-2">Выберите моба</label>
                        <select id="mob_id" name="mob_id" 
                                class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            <option value="">-- Выберите моба --</option>
                            @foreach($mobs as $mobOption)
                                <option value="{{ $mobOption->id }}" {{ request('mob_id') == $mobOption->id ? 'selected' : '' }}>
                                    {{ $mobOption->name }} (ID: {{ $mobOption->id }}) - {{ $mobOption->location ?? 'Без локации' }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <button type="submit" 
                            class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                        🔍 Выбрать
                    </button>
                </form>
            </div>
        </div>

        @if($mob)
        {{-- Информация о выбранном мобе --}}
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-2xl mr-3">📋</span>
                            Информация о мобе: {{ $mob->name }}
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">HP</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]">{{ $mob->hp }}/{{ $mob->max_hp }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Сила</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]">{{ $mob->strength }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Защита</div>
                                <div class="text-lg font-semibold text-[#d4cbb0]">{{ $mob->defense }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-[#c1a96e] mb-1">Локация</div>
                                <div class="text-sm text-[#d4cbb0]">{{ $mob->location ?? 'Не указана' }}</div>
                            </div>
                        </div>

                        {{-- Текущие скиллы моба --}}
                        @if($mob->skills->count() > 0)
                        <div class="border-t border-[#3b3629] pt-6">
                            <h4 class="text-lg font-semibold text-[#e4d7b0] mb-4">Текущие скиллы ({{ $mob->skills->count() }})</h4>
                            <div class="space-y-3">
                                @foreach($mob->skills as $mobSkill)
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        @if($mobSkill->skillTemplate)
                                            <div class="flex items-center">
                                                @if($mobSkill->skillTemplate->icon)
                                                    <img src="{{ asset($mobSkill->skillTemplate->icon) }}" alt="{{ $mobSkill->skillTemplate->name }}" class="w-8 h-8 mr-3">
                                                @endif
                                                <div>
                                                    <div class="font-semibold text-[#d4cbb0]">{{ $mobSkill->skillTemplate->name }}</div>
                                                    <div class="text-sm text-[#998d66]">
                                                        Шанс: {{ $mobSkill->chance ?? $mobSkill->skillTemplate->chance }}% | 
                                                        Тип: {{ ucfirst($mobSkill->skillTemplate->effect_type) }}
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <div class="text-[#998d66]">Шаблон скилла не найден</div>
                                        @endif
                                    </div>
                                    <form method="POST" action="{{ route('admin.mob-skills.detach') }}" 
                                          onsubmit="return confirm('Отвязать этот скилл от моба?')" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="mob_skill_id" value="{{ $mobSkill->id }}">
                                        <button type="submit" 
                                                class="bg-gradient-to-b from-[#59372d] to-[#3c221b] hover:from-[#6e3f35] hover:to-[#4a2a20] text-[#f8eac2] px-3 py-2 rounded border border-[#6e3f35] transition duration-300 text-sm">
                                            🗑️ Отвязать
                                        </button>
                                    </form>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @else
                        <div class="border-t border-[#3b3629] pt-6 text-center">
                            <div class="text-4xl mb-2">⚔️</div>
                            <div class="text-[#998d66]">У этого моба пока нет скиллов</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            {{-- Статистика --}}
            <div>
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">📊</span>
                            Статистика
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Скиллов у моба:</span>
                            <span class="text-[#c1a96e] font-semibold">{{ $mob->skills->count() }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Доступно шаблонов:</span>
                            <span class="text-[#c1a96e] font-semibold">{{ $templates->count() }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Общий уровень:</span>
                            <span class="text-[#c1a96e] font-semibold">{{ $mob->strength + $mob->defense + $mob->agility + $mob->vitality + $mob->intelligence }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Доступные скиллы для привязки --}}
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
            <div class="px-6 py-4 border-b border-[#3b3629]">
                <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                    <span class="text-2xl mr-3">⚔️</span>
                    Доступные скиллы для привязки
                </h3>
            </div>
            <div class="p-6">
                @if($templates->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($templates as $template)
                    @php
                        $alreadyAssigned = $mob->skills->where('skill_template_id', $template->id)->first();
                    @endphp
                    <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg p-4 {{ $alreadyAssigned ? 'opacity-50' : '' }}">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center">
                                @if($template->icon)
                                    <img src="{{ asset($template->icon) }}" alt="{{ $template->name }}" class="w-10 h-10 mr-3">
                                @endif
                                <div>
                                    <h4 class="font-semibold text-[#d4cbb0]">{{ $template->name }}</h4>
                                    <div class="text-sm text-[#998d66]">{{ ucfirst($template->effect_type) }}</div>
                                </div>
                            </div>
                            <span class="inline-block px-2 py-1 rounded text-xs font-medium
                                @if($template->effect_type === 'stun') bg-red-900/30 text-red-300 border border-red-700
                                @elseif($template->effect_type === 'buff') bg-green-900/30 text-green-300 border border-green-700
                                @elseif($template->effect_type === 'debuff') bg-orange-900/30 text-orange-300 border border-orange-700
                                @elseif($template->effect_type === 'dot') bg-purple-900/30 text-purple-300 border border-purple-700
                                @else bg-gray-900/30 text-gray-300 border border-gray-700
                                @endif">
                                {{ $template->priority }}
                            </span>
                        </div>

                        <p class="text-sm text-[#998d66] mb-4 line-clamp-2">{{ $template->description }}</p>

                        <div class="grid grid-cols-3 gap-2 text-xs text-center mb-4">
                            <div>
                                <div class="text-[#c1a96e]">Шанс</div>
                                <div class="text-[#d4cbb0]">{{ $template->chance }}%</div>
                            </div>
                            <div>
                                <div class="text-[#c1a96e]">Кулдаун</div>
                                <div class="text-[#d4cbb0]">{{ $template->cooldown }}с</div>
                            </div>
                            <div>
                                <div class="text-[#c1a96e]">Длительность</div>
                                <div class="text-[#d4cbb0]">{{ $template->duration }}с</div>
                            </div>
                        </div>

                        @if($alreadyAssigned)
                            <div class="text-center text-sm text-[#998d66] py-2">
                                ✓ Уже привязан к мобу
                            </div>
                        @else
                            <form method="POST" action="{{ route('admin.mob-skills.attach') }}">
                                @csrf
                                <input type="hidden" name="mob_id" value="{{ $mob->id }}">
                                <input type="hidden" name="skill_template_id" value="{{ $template->id }}">
                                
                                <div class="mb-3">
                                    <label class="block text-xs text-[#c1a96e] mb-1">Шанс срабатывания (%)</label>
                                    <input type="number" name="chance" min="1" max="100" value="{{ $template->chance }}"
                                           class="w-full bg-[#2a2722] border border-[#3b3629] rounded px-2 py-1 text-sm text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none">
                                </div>
                                
                                <button type="submit" 
                                        class="w-full bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-3 py-2 rounded border border-[#3b3629] transition duration-300 text-sm">
                                    🔗 Привязать к мобу
                                </button>
                            </form>
                        @endif
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">⚔️</div>
                    <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">Нет доступных скиллов</h3>
                    <p class="text-[#998d66] mb-6">Сначала создайте шаблоны скиллов</p>
                    <a href="{{ route('admin.mob-skills.create') }}" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                        ✨ Создать скилл
                    </a>
                </div>
                @endif
            </div>
        </div>
        @else
        {{-- Сообщение о выборе моба --}}
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
            <div class="p-12 text-center">
                <div class="text-6xl mb-4">👹</div>
                <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">Выберите моба</h3>
                <p class="text-[#998d66]">Для привязки скиллов сначала выберите моба из списка выше</p>
            </div>
        </div>
        @endif
    </div>
</body>
</html>
