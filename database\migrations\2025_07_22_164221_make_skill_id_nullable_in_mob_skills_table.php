<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Делает поле skill_id nullable в таблице mob_skills
     * так как теперь используется skill_template_id
     */
    public function up(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Делаем skill_id nullable
            $table->unsignedBigInteger('skill_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Возвращаем skill_id как NOT NULL
            $table->unsignedBigInteger('skill_id')->nullable(false)->change();
        });
    }
};
