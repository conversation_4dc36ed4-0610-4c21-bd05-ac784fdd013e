<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MobSkillTemplate;

class MobSkillTemplatesSeeder extends Seeder
{
    /**
     * Заполняет таблицу шаблонов скиллов мобов начальными данными
     */
    public function run(): void
    {
        // Создаем скилл "Тяжелый удар"
        MobSkillTemplate::updateOrCreate(
            ['name' => 'Тяжелый удар'],
            [
                'description' => 'Мощный удар, который может оглушить противника, лишив его возможности использовать умения и перемещаться между локациями.',
                'icon' => 'assets/skills/mobs/skillHeavyStrike.png',
                'effect_type' => 'stun',
                'effect_data' => [
                    'duration' => 5, // 5 секунд стана
                    'disable_skills' => true, // Отключает использование умений
                    'disable_movement' => true, // Отключает переходы между локациями
                    'disable_actions' => true, // Отключает другие действия
                    'message' => '⚡ Вы оглушены тяжелым ударом и не можете действовать!'
                ],
                'chance' => 15, // 15% шанс использования
                'cooldown' => 30, // 30 секунд кулдаун
                'duration' => 5, // 5 секунд длительность эффекта
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 8 // Высокий приоритет
            ]
        );

        // Создаем дополнительные базовые скиллы для разнообразия
        MobSkillTemplate::updateOrCreate(
            ['name' => 'Ярость'],
            [
                'description' => 'Моб впадает в ярость, увеличивая свой урон на короткое время.',
                'icon' => 'assets/skills/mobs/skillRage.png',
                'effect_type' => 'buff',
                'effect_data' => [
                    'damage_multiplier' => 1.5, // +50% к урону
                    'duration' => 10,
                    'message' => '🔥 Моб впал в ярость!'
                ],
                'chance' => 20,
                'cooldown' => 45,
                'duration' => 10,
                'target_type' => 'self',
                'min_health_percent' => 0,
                'max_health_percent' => 50, // Использует только при низком здоровье
                'is_active' => true,
                'priority' => 6
            ]
        );

        MobSkillTemplate::updateOrCreate(
            ['name' => 'Кровотечение'],
            [
                'description' => 'Наносит рану, которая причиняет урон со временем.',
                'icon' => 'assets/skills/mobs/skillBleed.png',
                'effect_type' => 'dot',
                'effect_data' => [
                    'damage_per_tick' => 10,
                    'tick_interval' => 2, // Каждые 2 секунды
                    'total_duration' => 12, // 12 секунд общая длительность
                    'message' => '🩸 Вы истекаете кровью!'
                ],
                'chance' => 25,
                'cooldown' => 20,
                'duration' => 12,
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 5
            ]
        );

        MobSkillTemplate::updateOrCreate(
            ['name' => 'Исцеление'],
            [
                'description' => 'Моб восстанавливает часть своего здоровья.',
                'icon' => 'assets/skills/mobs/skillHeal.png',
                'effect_type' => 'heal',
                'effect_data' => [
                    'heal_amount' => 50,
                    'heal_percent' => 15, // 15% от максимального здоровья
                    'message' => '✨ Моб восстанавливает здоровье!'
                ],
                'chance' => 30,
                'cooldown' => 60,
                'duration' => 0, // Мгновенный эффект
                'target_type' => 'self',
                'min_health_percent' => 0,
                'max_health_percent' => 30, // Использует только при критически низком здоровье
                'is_active' => true,
                'priority' => 9 // Очень высокий приоритет для выживания
            ]
        );

        MobSkillTemplate::updateOrCreate(
            ['name' => 'Ослабление'],
            [
                'description' => 'Ослабляет противника, снижая его урон и защиту.',
                'icon' => 'assets/skills/mobs/skillWeaken.png',
                'effect_type' => 'debuff',
                'effect_data' => [
                    'damage_reduction' => 0.3, // -30% к урону
                    'defense_reduction' => 0.2, // -20% к защите
                    'duration' => 15,
                    'message' => '💀 Вы ослаблены!'
                ],
                'chance' => 18,
                'cooldown' => 35,
                'duration' => 15,
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 7
            ]
        );
    }
}
