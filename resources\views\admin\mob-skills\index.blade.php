<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Управление скиллами мобов - Админ панель</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        {{-- Заголовок страницы --}}
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">⚔️ Управление скиллами мобов</h1>
                    <p class="text-[#998d66]">Создание и настройка скиллов для мобов в игре</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.dashboard') }}" 
                       class="bg-[#2a2722] hover:bg-[#3b3629] text-[#f8eac2] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад в админку
                    </a>
                    <a href="{{ route('admin.mob-skills.create') }}" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-2 rounded-lg border border-[#3b3629] transition duration-300 shadow-lg">
                        ✨ Создать скилл
                    </a>
                </div>
            </div>
        </div>

        {{-- Статистика --}}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-[#3b3629] rounded-lg flex items-center justify-center mr-4">
                        <span class="text-2xl">📋</span>
                    </div>
                    <div>
                        <p class="text-[#998d66] text-sm">Всего шаблонов</p>
                        <p class="text-2xl font-bold text-[#e4d7b0]">{{ $stats['total_templates'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-[#2f473c] rounded-lg flex items-center justify-center mr-4">
                        <span class="text-2xl">✅</span>
                    </div>
                    <div>
                        <p class="text-[#998d66] text-sm">Активных шаблонов</p>
                        <p class="text-2xl font-bold text-[#c1a96e]">{{ $stats['active_templates'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-[#6e3f35] rounded-lg flex items-center justify-center mr-4">
                        <span class="text-2xl">🎯</span>
                    </div>
                    <div>
                        <p class="text-[#998d66] text-sm">Привязок к мобам</p>
                        <p class="text-2xl font-bold text-[#e4d7b0]">{{ $stats['total_mob_skills'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629]">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-[#3b3629] rounded-lg flex items-center justify-center mr-4">
                        <span class="text-2xl">👹</span>
                    </div>
                    <div>
                        <p class="text-[#998d66] text-sm">Мобов со скиллами</p>
                        <p class="text-2xl font-bold text-[#c1a96e]">{{ $stats['mobs_with_skills'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        {{-- Быстрые действия --}}
        <div class="bg-[#2a2721] p-6 rounded-lg border border-[#3b3629] mb-8">
            <h3 class="text-xl font-bold text-[#e4d7b0] mb-4">🚀 Быстрые действия</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('admin.mob-skills.assign') }}" 
                   class="bg-[#3b3629] hover:bg-[#4a452c] p-4 rounded-lg border border-[#514b3c] transition duration-300 text-center">
                    <div class="text-2xl mb-2">🔗</div>
                    <div class="font-semibold text-[#e4d7b0]">Привязать скилл к мобу</div>
                    <div class="text-sm text-[#998d66] mt-1">Назначить скиллы конкретным мобам</div>
                </a>
                
                <a href="{{ route('admin.mobs.index') }}" 
                   class="bg-[#3b3629] hover:bg-[#4a452c] p-4 rounded-lg border border-[#514b3c] transition duration-300 text-center">
                    <div class="text-2xl mb-2">👹</div>
                    <div class="font-semibold text-[#e4d7b0]">Управление мобами</div>
                    <div class="text-sm text-[#998d66] mt-1">Перейти к настройке мобов</div>
                </a>
                
                <div class="bg-[#3b3629] hover:bg-[#4a452c] p-4 rounded-lg border border-[#514b3c] transition duration-300 text-center cursor-pointer"
                     onclick="window.location.reload()">
                    <div class="text-2xl mb-2">🔄</div>
                    <div class="font-semibold text-[#e4d7b0]">Обновить данные</div>
                    <div class="text-sm text-[#998d66] mt-1">Перезагрузить статистику</div>
                </div>
            </div>
        </div>

        {{-- Список шаблонов скиллов --}}
        <div class="bg-[#2a2721] rounded-lg border border-[#3b3629] overflow-hidden">
            <div class="p-6 border-b border-[#3b3629]">
                <h3 class="text-xl font-bold text-[#e4d7b0]">📋 Шаблоны скиллов</h3>
            </div>

            @if($templates->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-[#1a1814]">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Скилл</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Тип</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Шанс</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Кулдаун</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Приоритет</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Статус</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#998d66] uppercase tracking-wider">Действия</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-[#3b3629]">
                            @foreach($templates as $template)
                                <tr class="hover:bg-[#1a1814] transition duration-200">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            @if($template->icon)
                                                <img src="{{ asset($template->icon) }}" alt="{{ $template->name }}" 
                                                     class="w-8 h-8 rounded mr-3" onerror="this.style.display='none'">
                                            @endif
                                            <div>
                                                <div class="text-sm font-medium text-[#e4d7b0]">{{ $template->name }}</div>
                                                <div class="text-sm text-[#998d66]">{{ Str::limit($template->description, 50) }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $template->effect_type_icon_class }}">
                                            {{ $template->effect_type_emoji }} {{ $template->effect_type_name }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-[#d4cbb0]">{{ $template->formatted_chance }}</td>
                                    <td class="px-6 py-4 text-sm text-[#d4cbb0]">{{ $template->formatted_cooldown }}</td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= $template->priority; $i++)
                                                <span class="text-[#c1a96e]">⭐</span>
                                            @endfor
                                            <span class="ml-2 text-sm text-[#998d66]">{{ $template->priority }}/10</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        @if($template->is_active)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
                                                ✅ Активен
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900 text-red-300">
                                                ❌ Неактивен
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('admin.mob-skills.show', $template) }}"
                                               class="text-[#c1a96e] hover:text-[#e4d7b0] transition duration-200" title="Просмотр">👁️</a>
                                            <a href="{{ route('admin.mob-skills.edit', $template) }}"
                                               class="text-blue-400 hover:text-blue-300 transition duration-200" title="Редактировать">✏️</a>
                                            <form action="{{ route('admin.mob-skills.destroy', $template) }}" method="POST" class="inline"
                                                  onsubmit="return confirm('Вы уверены, что хотите удалить этот шаблон скилла?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-400 hover:text-red-300 transition duration-200">🗑️</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                {{-- Пагинация --}}
                <div class="px-6 py-4 border-t border-[#3b3629]">
                    {{ $templates->links() }}
                </div>
            @else
                <div class="p-12 text-center">
                    <div class="text-6xl mb-4">📋</div>
                    <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">Нет шаблонов скиллов</h3>
                    <p class="text-[#998d66] mb-6">Создайте первый шаблон скилла для мобов</p>
                    <a href="{{ route('admin.mob-skills.create') }}" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300 shadow-lg">
                        ✨ Создать первый скилл
                    </a>
                </div>
            @endif
        </div>
    </div>

    {{-- Уведомления --}}
    @if(session('success'))
        <div class="fixed top-4 right-4 bg-green-900 border border-green-700 text-green-300 px-6 py-4 rounded-lg shadow-lg z-50">
            <div class="flex items-center">
                <span class="text-xl mr-3">✅</span>
                <span>{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="fixed top-4 right-4 bg-red-900 border border-red-700 text-red-300 px-6 py-4 rounded-lg shadow-lg z-50">
            <div class="flex items-center">
                <span class="text-xl mr-3">❌</span>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <script>
        // Автоматически скрывать уведомления через 5 секунд
        setTimeout(() => {
            const notifications = document.querySelectorAll('.fixed.top-4.right-4');
            notifications.forEach(notification => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            });
        }, 5000);
    </script>
</body>
</html>
