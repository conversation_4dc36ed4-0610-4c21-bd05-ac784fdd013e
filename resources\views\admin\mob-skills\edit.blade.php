<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Редактировать {{ $mobSkill->name }} - Админ панель</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        {{-- Заголовок страницы --}}
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">✏️ Редактировать скилл: {{ $mobSkill->name }}</h1>
                    <p class="text-[#998d66]">Изменение параметров скилла моба</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.mob-skills.show', $mobSkill) }}" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к просмотру
                    </a>
                </div>
            </div>
        </div>

        {{-- Форма редактирования --}}
        <form method="POST" action="{{ route('admin.mob-skills.update', $mobSkill) }}" class="space-y-8">
            @csrf
            @method('PUT')

            <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                <div class="px-6 py-4 border-b border-[#3b3629]">
                    <h2 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                        <span class="text-2xl mr-3">📋</span>
                        Основная информация
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    {{-- Название и иконка --}}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-[#c1a96e] mb-2">Название скилла *</label>
                            <input type="text" id="name" name="name" value="{{ old('name', $mobSkill->name) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="icon" class="block text-sm font-medium text-[#c1a96e] mb-2">Путь к иконке</label>
                            <input type="text" id="icon" name="icon" value="{{ old('icon', $mobSkill->icon) }}"
                                   placeholder="assets/skills/mobs/skillName.png"
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('icon')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    {{-- Описание --}}
                    <div>
                        <label for="description" class="block text-sm font-medium text-[#c1a96e] mb-2">Описание</label>
                        <textarea id="description" name="description" rows="4"
                                  class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300"
                                  placeholder="Подробное описание эффекта скилла...">{{ old('description', $mobSkill->description) }}</textarea>
                        @error('description')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    {{-- Тип эффекта и цель --}}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="effect_type" class="block text-sm font-medium text-[#c1a96e] mb-2">Тип эффекта *</label>
                            <select id="effect_type" name="effect_type" required
                                    class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                                <option value="stun" {{ old('effect_type', $mobSkill->effect_type) === 'stun' ? 'selected' : '' }}>Stun (Оглушение)</option>
                                <option value="damage" {{ old('effect_type', $mobSkill->effect_type) === 'damage' ? 'selected' : '' }}>Damage (Урон)</option>
                                <option value="heal" {{ old('effect_type', $mobSkill->effect_type) === 'heal' ? 'selected' : '' }}>Heal (Лечение)</option>
                                <option value="buff" {{ old('effect_type', $mobSkill->effect_type) === 'buff' ? 'selected' : '' }}>Buff (Усиление)</option>
                                <option value="debuff" {{ old('effect_type', $mobSkill->effect_type) === 'debuff' ? 'selected' : '' }}>Debuff (Ослабление)</option>
                                <option value="dot" {{ old('effect_type', $mobSkill->effect_type) === 'dot' ? 'selected' : '' }}>DOT (Урон со временем)</option>
                                <option value="hot" {{ old('effect_type', $mobSkill->effect_type) === 'hot' ? 'selected' : '' }}>HOT (Лечение со временем)</option>
                                <option value="teleport" {{ old('effect_type', $mobSkill->effect_type) === 'teleport' ? 'selected' : '' }}>Teleport (Телепорт)</option>
                                <option value="summon" {{ old('effect_type', $mobSkill->effect_type) === 'summon' ? 'selected' : '' }}>Summon (Призыв)</option>
                                <option value="special" {{ old('effect_type', $mobSkill->effect_type) === 'special' ? 'selected' : '' }}>Special (Особый)</option>
                            </select>
                            @error('effect_type')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="target_type" class="block text-sm font-medium text-[#c1a96e] mb-2">Тип цели *</label>
                            <select id="target_type" name="target_type" required
                                    class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                                <option value="player" {{ old('target_type', $mobSkill->target_type) === 'player' ? 'selected' : '' }}>Player (Игрок)</option>
                                <option value="mob" {{ old('target_type', $mobSkill->target_type) === 'mob' ? 'selected' : '' }}>Mob (Моб)</option>
                                <option value="self" {{ old('target_type', $mobSkill->target_type) === 'self' ? 'selected' : '' }}>Self (Себя)</option>
                                <option value="area" {{ old('target_type', $mobSkill->target_type) === 'area' ? 'selected' : '' }}>Area (Область)</option>
                            </select>
                            @error('target_type')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    {{-- Параметры скилла --}}
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                            <label for="chance" class="block text-sm font-medium text-[#c1a96e] mb-2">Шанс (%) *</label>
                            <input type="number" id="chance" name="chance" min="1" max="100" value="{{ old('chance', $mobSkill->chance) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('chance')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="cooldown" class="block text-sm font-medium text-[#c1a96e] mb-2">Кулдаун (сек) *</label>
                            <input type="number" id="cooldown" name="cooldown" min="0" value="{{ old('cooldown', $mobSkill->cooldown) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('cooldown')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="duration" class="block text-sm font-medium text-[#c1a96e] mb-2">Длительность (сек) *</label>
                            <input type="number" id="duration" name="duration" min="0" value="{{ old('duration', $mobSkill->duration) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('duration')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="priority" class="block text-sm font-medium text-[#c1a96e] mb-2">Приоритет *</label>
                            <input type="number" id="priority" name="priority" min="1" max="10" value="{{ old('priority', $mobSkill->priority) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('priority')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    {{-- Условия использования --}}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="min_health_percent" class="block text-sm font-medium text-[#c1a96e] mb-2">Мин. HP для использования (%) *</label>
                            <input type="number" id="min_health_percent" name="min_health_percent" min="0" max="100" value="{{ old('min_health_percent', $mobSkill->min_health_percent) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('min_health_percent')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="max_health_percent" class="block text-sm font-medium text-[#c1a96e] mb-2">Макс. HP для использования (%) *</label>
                            <input type="number" id="max_health_percent" name="max_health_percent" min="0" max="100" value="{{ old('max_health_percent', $mobSkill->max_health_percent) }}" required
                                   class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                            @error('max_health_percent')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    {{-- Данные эффекта --}}
                    <div>
                        <label for="effect_data" class="block text-sm font-medium text-[#c1a96e] mb-2">Данные эффекта (JSON)</label>
                        <textarea id="effect_data" name="effect_data" rows="8"
                                  class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300 font-mono text-sm"
                                  placeholder='{"damage": 50, "duration": 10, "message": "Сообщение об эффекте"}'>{{ old('effect_data', json_encode($mobSkill->effect_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) }}</textarea>
                        @error('effect_data')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-sm text-[#998d66] mt-2">
                            Введите данные эффекта в формате JSON. Примеры:<br>
                            • Стан: {"duration": 5, "disable_skills": true, "message": "Вы оглушены!"}<br>
                            • Урон: {"damage": 50, "message": "Получен урон!"}<br>
                            • DOT: {"damage_per_tick": 10, "tick_interval": 2, "total_duration": 12}
                        </p>
                    </div>

                    {{-- Статус активности --}}
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $mobSkill->is_active) ? 'checked' : '' }}
                               class="w-4 h-4 text-[#c1a96e] bg-[#1a1814] border-[#3b3629] rounded focus:ring-[#c1a96e] focus:ring-2">
                        <label for="is_active" class="ml-2 text-sm font-medium text-[#c1a96e]">Скилл активен</label>
                    </div>
                </div>
            </div>

            {{-- Кнопки действий --}}
            <div class="flex justify-end space-x-4">
                <a href="{{ route('admin.mob-skills.show', $mobSkill) }}" 
                   class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                    Отмена
                </a>
                <button type="submit" 
                        class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                    💾 Сохранить изменения
                </button>
            </div>
        </form>
    </div>

    {{-- JavaScript для валидации JSON --}}
    <script>
        document.getElementById('effect_data').addEventListener('blur', function() {
            const textarea = this;
            const value = textarea.value.trim();
            
            if (value) {
                try {
                    JSON.parse(value);
                    textarea.style.borderColor = '#3b3629';
                } catch (e) {
                    textarea.style.borderColor = '#ef4444';
                }
            }
        });
    </script>
</body>
</html>
