<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Создание таблицы шаблонов скиллов мобов
     * Эта таблица хранит глобальные шаблоны скиллов, которые можно привязывать к разным мобам
     */
    public function up(): void
    {
        Schema::create('mob_skill_templates', function (Blueprint $table) {
            $table->id();
            
            // Основная информация о скилле
            $table->string('name')->comment('Название скилла');
            $table->text('description')->nullable()->comment('Описание скилла');
            $table->string('icon')->nullable()->comment('Путь к иконке скилла');
            
            // Тип эффекта и данные
            $table->enum('effect_type', [
                'stun',           // Оглушение
                'damage',         // Дополнительный урон
                'heal',           // Лечение
                'buff',           // Положительный эффект
                'debuff',         // Отрицательный эффект
                'dot',            // Урон со временем
                'hot',            // Лечение со временем
                'teleport',       // Телепортация
                'summon',         // Призыв
                'special'         // Специальный эффект
            ])->comment('Тип эффекта скилла');
            
            $table->json('effect_data')->nullable()->comment('Данные эффекта в JSON формате');
            
            // Параметры использования
            $table->integer('chance')->default(10)->comment('Шанс использования скилла в процентах (1-100)');
            $table->integer('cooldown')->default(0)->comment('Время перезарядки в секундах');
            $table->integer('duration')->default(0)->comment('Длительность эффекта в секундах');
            
            // Условия применения
            $table->enum('target_type', ['player', 'mob', 'self', 'area'])->default('player')->comment('Тип цели');
            $table->integer('min_health_percent')->default(0)->comment('Минимальный процент здоровья для использования');
            $table->integer('max_health_percent')->default(100)->comment('Максимальный процент здоровья для использования');
            
            // Системные поля
            $table->boolean('is_active')->default(true)->comment('Активен ли шаблон скилла');
            $table->integer('priority')->default(1)->comment('Приоритет использования (чем выше, тем приоритетнее)');
            
            $table->timestamps();
            
            // Индексы для оптимизации
            $table->index(['is_active', 'effect_type'], 'idx_mob_skill_templates_active_type');
            $table->index(['chance', 'is_active'], 'idx_mob_skill_templates_chance_active');
            $table->index('priority', 'idx_mob_skill_templates_priority');
        });
    }

    /**
     * Откат миграции
     */
    public function down(): void
    {
        Schema::dropIfExists('mob_skill_templates');
    }
};
