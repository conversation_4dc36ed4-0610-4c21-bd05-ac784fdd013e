<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Models\MineLocation;

class DiagnoseMobSkills extends Command
{
    protected $signature = 'diagnose:mob-skills {--mob-name=Огр : Название моба для диагностики}';
    protected $description = 'Диагностика системы скиллов мобов';

    public function handle()
    {
        $mobName = $this->option('mob-name');
        
        $this->info("🔍 Диагностика системы скиллов мобов");
        $this->info("Ищем мобов с именем: {$mobName}");
        $this->newLine();

        // 1. Проверяем шаблон скилла "Тяжелый удар"
        $this->checkHeavyStrikeTemplate();
        
        // 2. Проверяем мобов Огр
        $this->checkOgreMobs($mobName);
        
        // 3. Проверяем привязки скиллов
        $this->checkSkillAssignments($mobName);
        
        // 4. Проверяем интеграцию с боевой системой
        $this->checkBattleIntegration();

        $this->newLine();
        $this->info("✅ Диагностика завершена");
    }

    private function checkHeavyStrikeTemplate()
    {
        $this->info("📋 Проверка шаблона скилла 'Тяжелый удар':");
        
        $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        
        if (!$template) {
            $this->error("❌ Шаблон скилла 'Тяжелый удар' не найден!");
            $this->info("💡 Запустите: php artisan db:seed --class=MobSkillTemplatesSeeder");
            return;
        }

        $this->info("✅ Шаблон найден (ID: {$template->id})");
        $this->info("   Активен: " . ($template->is_active ? 'Да' : 'Нет'));
        $this->info("   Шанс: {$template->chance}%");
        $this->info("   Кулдаун: {$template->cooldown} сек");
        $this->info("   Тип эффекта: {$template->effect_type}");
        
        if ($template->effect_data) {
            $this->info("   Данные эффекта:");
            foreach ($template->effect_data as $key => $value) {
                $this->info("     - {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value));
            }
        }

        if (!$template->is_active) {
            $this->warn("⚠️  Шаблон скилла неактивен!");
        }
        
        $this->newLine();
    }

    private function checkOgreMobs($mobName)
    {
        $this->info("👹 Проверка мобов '{$mobName}':");
        
        $ogres = Mob::where('name', 'like', "%{$mobName}%")->get();
        
        if ($ogres->isEmpty()) {
            $this->error("❌ Мобы с именем '{$mobName}' не найдены!");
            return;
        }

        $this->info("✅ Найдено мобов: {$ogres->count()}");
        
        foreach ($ogres as $ogre) {
            $this->info("   Огр ID: {$ogre->id}");
            $this->info("     Название: {$ogre->name}");
            $this->info("     HP: {$ogre->hp}/{$ogre->max_hp}");
            $this->info("     Локация: {$ogre->location_id}");
            
            if ($ogre->mine_location_id) {
                $mineLocation = MineLocation::find($ogre->mine_location_id);
                $this->info("     Рудник: {$ogre->mine_location_id}" . 
                    ($mineLocation ? " ({$mineLocation->name})" : " (не найден)"));
            }
            
            $this->info("     Тип моба: " . ($ogre->mob_type ?? 'не указан'));
            $this->newLine();
        }
    }

    private function checkSkillAssignments($mobName)
    {
        $this->info("🔗 Проверка привязок скиллов:");
        
        $ogres = Mob::where('name', 'like', "%{$mobName}%")->get();
        $heavyStrikeTemplate = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        
        if (!$heavyStrikeTemplate) {
            $this->error("❌ Шаблон 'Тяжелый удар' не найден, пропускаем проверку привязок");
            return;
        }

        foreach ($ogres as $ogre) {
            $this->info("   Огр '{$ogre->name}' (ID: {$ogre->id}):");
            
            $skills = $ogre->skills()->with('skillTemplate')->get();
            
            if ($skills->isEmpty()) {
                $this->warn("     ⚠️  Нет привязанных скиллов");
                $this->info("     💡 Привяжите скилл через админку: /admin/mob-skills/assign");
            } else {
                $this->info("     ✅ Привязанные скиллы:");
                foreach ($skills as $skill) {
                    $templateName = $skill->skillTemplate->name ?? 'Неизвестный';
                    $this->info("       - {$templateName} (шанс: {$skill->chance}%)");
                    
                    if ($skill->cooldown_ends_at && $skill->cooldown_ends_at->isFuture()) {
                        $this->info("         Кулдаун до: {$skill->cooldown_ends_at}");
                    }
                }
            }
            
            // Проверяем конкретно скилл "Тяжелый удар"
            $heavyStrikeSkill = $skills->where('skill_template_id', $heavyStrikeTemplate->id)->first();
            if (!$heavyStrikeSkill) {
                $this->warn("     ⚠️  Скилл 'Тяжелый удар' не привязан к этому мобу");
            } else {
                $this->info("     ✅ Скилл 'Тяжелый удар' привязан (шанс: {$heavyStrikeSkill->chance}%)");
            }
            
            $this->newLine();
        }
    }

    private function checkBattleIntegration()
    {
        $this->info("⚔️  Проверка интеграции с боевой системой:");
        
        // Проверяем наличие MobSkillIntegrationService
        if (class_exists('App\Services\Mine\MobSkillIntegrationService')) {
            $this->info("✅ MobSkillIntegrationService найден");
        } else {
            $this->error("❌ MobSkillIntegrationService не найден");
        }
        
        // Проверяем интеграцию в MobAutoAttackJob
        $jobFile = app_path('Jobs/MobAutoAttackJob.php');
        if (file_exists($jobFile)) {
            $content = file_get_contents($jobFile);
            if (strpos($content, 'MobSkillIntegrationService') !== false) {
                $this->info("✅ MobAutoAttackJob интегрирован с системой скиллов");
            } else {
                $this->warn("⚠️  MobAutoAttackJob НЕ интегрирован с системой скиллов");
                $this->info("💡 Требуется добавить вызовы processMobAttackSkills()");
            }
        }
        
        $this->newLine();
    }
}
