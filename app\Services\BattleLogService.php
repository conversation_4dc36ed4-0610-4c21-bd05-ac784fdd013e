<?php
namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

class BattleLogService
{
    /**
     * Время жизни логов в секундах (1 час)
     */
    private const LOGS_TTL = 3600;

    /**
     * Максимальное количество логов для хранения
     */
    private const MAX_LOGS = 12;

    /**
     * Получает ключ для общих боевых логов пользователя
     *
     * @param int $userId ID пользователя
     * @return string Ключ логов
     */
    public function getBattleLogKey($userId): string
    {
        return "battle_logs:{$userId}";
    }

    /**
     * Получает последние логи из Redis.
     */
    public function getLogs(string $key, ?string $userRace = null): array
    {
        // Формируем полный ключ для Redis
        $redisKey = "{$key}:logs";

        // Проверяем, существует ли ключ в Redis
        if (Redis::exists($redisKey) == 0) {
            // Если это специфический ключ локации, попробуем проверить общий ключ
            $userId = null;
            if (strpos($key, 'location:') === 0) {
                $parts = explode(':', $key);
                $userId = end($parts);

                if (is_numeric($userId)) {
                    $commonKey = $this->getBattleLogKey($userId);
                    $commonRedisKey = $commonKey . ':logs';

                    if (Redis::exists($commonRedisKey) > 0) {
                        Log::info('Найдены логи в общем ключе вместо специфического', [
                            'requested_key' => $key,
                            'common_key' => $commonKey
                        ]);
                        $key = $commonKey;
                        $redisKey = $commonRedisKey;
                    } else {
                        // Проверим ключи других локаций
                        $tarnmoreKey = "location:tarnmore_quarry:{$userId}";
                        $tarnmoreRedisKey = $tarnmoreKey . ':logs';

                        if (Redis::exists($tarnmoreRedisKey) > 0) {
                            Log::info('Найдены логи в ключе Тарнмора', [
                                'requested_key' => $key,
                                'tarnmore_key' => $tarnmoreKey
                            ]);
                            $key = $tarnmoreKey;
                            $redisKey = $tarnmoreRedisKey;
                        }
                    }
                }
            }

            // Если логи не найдены, добавляем отладочную информацию
            if (Redis::exists($redisKey) == 0) {
                Log::warning('Логи не найдены в Redis', [
                    'key' => $key,
                    'redis_key' => $redisKey,
                    'user_id' => $userId ?? 'неизвестно'
                ]);
                return [];
            }
        }

        // Получаем данные из Redis
        $redisLogs = Redis::lrange($redisKey, 0, -1);
        if (empty($redisLogs)) {
            return [];
        }

        // Преобразуем данные из Redis в массив логов
        $logs = [];
        foreach ($redisLogs as $item) {
            $log = json_decode($item, true);
            if ($log) {
                // Проверяем и исправляем структуру лога
                if (!isset($log['type'])) {
                    $log['type'] = 'info';
                }
                if (!isset($log['message'])) {
                    $log['message'] = 'Содержимое лога отсутствует';
                }
                if (!isset($log['timestamp'])) {
                    $log['timestamp'] = now()->format('H:i');
                }
                $logs[] = $log;
            }
        }

        // Применяем фильтрацию по расе для логов наград обелиска, если указана раса пользователя
        if ($userRace) {
            $logs = $this->filterObeliskRewardLogsByRace($logs, $userRace);
        }

        // Log::info('Получены логи из Redis', [
        //     'key' => $redisKey,
        //     'logs_count' => count($logs)
        // ]);

        return $logs; // Логи уже в нужном порядке (новые сверху)
    }

    /**
     * Добавляет отладочную информацию о логе
     */
    public function logDebugInfo(string $key, string $message): void
    {
        $redisKey = "{$key}:logs";
        $logsCount = Redis::llen($redisKey);

        Log::debug("BattleLogService добавление лога: {$message}", [
            'key' => $key,
            'redis_key' => $redisKey,
            'logs_count' => $logsCount,
            'redis_exists' => Redis::exists($redisKey) > 0
        ]);
    }

    /**
     * Логирует использование скилла моба с шаблоном
     *
     * @param int $mobId ID моба
     * @param string $mobName Имя моба
     * @param int|null $targetId ID цели
     * @param string $targetName Имя цели
     * @param string $skillName Название скилла
     * @param string $effectType Тип эффекта
     * @param array $effectData Данные эффекта
     * @param string $locationKey Ключ локации
     */
    public function logMobSkillUsage(
        int $mobId,
        string $mobName,
        ?int $targetId,
        string $targetName,
        string $skillName,
        string $effectType,
        array $effectData = [],
        string $locationKey = 'unknown'
    ): void {
        try {
            // Формируем сообщение в зависимости от типа эффекта
            $message = $this->formatMobSkillMessage($mobName, $targetName, $skillName, $effectType, $effectData);

            // Определяем тип лога
            $logType = $this->getMobSkillLogType($effectType);

            // Определяем ключ для логов
            $battleLogKey = $targetId ? "location:{$locationKey}:{$targetId}" : "location:{$locationKey}:{$mobId}";

            // Добавляем лог
            $this->addLog($battleLogKey, $message, $logType);

            Log::info('Добавлен лог скилла моба', [
                'mob_id' => $mobId,
                'mob_name' => $mobName,
                'target_id' => $targetId,
                'skill_name' => $skillName,
                'effect_type' => $effectType,
                'battle_log_key' => $battleLogKey
            ]);

        } catch (\Exception $e) {
            Log::error('Ошибка при логировании скилла моба', [
                'mob_id' => $mobId,
                'skill_name' => $skillName,
                'effect_type' => $effectType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Форматирует сообщение для лога скилла моба
     */
    protected function formatMobSkillMessage(
        string $mobName,
        string $targetName,
        string $skillName,
        string $effectType,
        array $effectData = []
    ): string {
        switch ($effectType) {
            case 'stun':
                $duration = $effectData['duration'] ?? 5;
                return "⚡ {$mobName} оглушил {$targetName} скиллом '{$skillName}' на {$duration} сек!";

            case 'damage':
                $damage = $effectData['damage'] ?? 0;
                return "💥 {$mobName} нанес {$damage} урона {$targetName} скиллом '{$skillName}'!";

            case 'buff':
                $duration = $effectData['duration'] ?? 0;
                return "💪 {$mobName} получил усиление '{$skillName}' на {$duration} сек!";

            case 'debuff':
                $duration = $effectData['duration'] ?? 0;
                return "💀 {$mobName} ослабил {$targetName} скиллом '{$skillName}' на {$duration} сек!";

            case 'dot':
                $damage = $effectData['damage_per_tick'] ?? 0;
                $duration = $effectData['total_duration'] ?? 0;
                return "🩸 {$mobName} наложил кровотечение на {$targetName} скиллом '{$skillName}' ({$damage} урона каждые 2 сек в течение {$duration} сек)!";

            case 'hot':
                $heal = $effectData['heal_per_tick'] ?? 0;
                $duration = $effectData['total_duration'] ?? 0;
                return "💚 {$mobName} наложил регенерацию скиллом '{$skillName}' ({$heal} лечения каждые 2 сек в течение {$duration} сек)!";

            case 'heal':
                $healAmount = $effectData['heal_amount'] ?? 0;
                return "💚 {$mobName} восстановил {$healAmount} HP скиллом '{$skillName}'!";

            default:
                return "✨ {$mobName} использовал '{$skillName}' на {$targetName}!";
        }
    }

    /**
     * Определяет тип лога для скилла моба
     */
    protected function getMobSkillLogType(string $effectType): string
    {
        switch ($effectType) {
            case 'stun':
            case 'damage':
            case 'debuff':
            case 'dot':
                return 'danger';

            case 'buff':
            case 'hot':
            case 'heal':
                return 'success';

            default:
                return 'info';
        }
    }

    /**
     * Обновляет логи в Redis
     */
    private function updateRedisLogs(string $redisKey, array $logs): void
    {
        Redis::del($redisKey);
        foreach ($logs as $log) {
            Redis::lpush($redisKey, json_encode($log));
        }
        Redis::expire($redisKey, self::LOGS_TTL);
    }

    /**
     * Добавляет лог в Redis и ограничивает их количество.
     */
    public function addLog(string $key, string $message, string $type = 'info'): void
    {
        // ИСПРАВЛЕНИЕ: Проверяем, нужна ли миграция для унификации всех логов
        if (strpos($key, 'battle_logs:') === 0 && !strpos($key, 'battle_logs:mines:')) {
            // Это персональный ключ игрока - проверяем миграцию
            $this->migrateToUnifiedBattleLogsIfNeeded($key);
        } elseif (strpos($key, 'battle_logs:mines:') === 0) {
            // Старая логика для рудников (оставляем для совместимости)
            $this->migrateLegacyMineLogsIfNeeded($key);
        }

        // Формируем полный ключ для Redis
        $redisKey = "{$key}:logs";

        // Добавить отладку
        Log::info("Добавление лога в журнал боя", [
            'key' => $key,
            'redis_key' => $redisKey,
            'message_start' => substr($message, 0, 30),
            'type' => $type
        ]);

        // Добавляем уникальную метку времени к сообщениям, чтобы избежать определения как дубликатов
        $uniqueTimestamp = microtime(true);
        $message .= "<!-- ts:{$uniqueTimestamp} -->";

        // Формируем новую запись лога
        $newLog = [
            'message' => $message,
            'type' => $type,
            'timestamp' => now()->format('H:i'),
            'created_at' => now()->timestamp
        ];

        // ОПТИМИЗАЦИЯ: Используем pipeline для выполнения всех операций за один раз
        Redis::pipeline(function ($pipe) use ($redisKey, $newLog) {
            // Добавляем новый лог в начало списка
            $pipe->lpush($redisKey, json_encode($newLog));

            // Ограничиваем количество логов
            $pipe->ltrim($redisKey, 0, self::MAX_LOGS - 1);

            // Устанавливаем время жизни ключа
            $pipe->expire($redisKey, self::LOGS_TTL);
        });
    }

    /**
     * Фильтрует логи наград обелиска по расе игрока
     *
     * @param array $logs Массив логов
     * @param string $userRace Раса игрока (solarius или lunarius)
     * @return array Отфильтрованные логи
     */
    private function filterObeliskRewardLogsByRace(array $logs, string $userRace): array
    {
        return array_values(array_filter($logs, function ($log) use ($userRace) {
            $message = $log['message'] ?? '';

            // Проверяем, является ли это сообщением о награде обелиска
            if (!$this->isObeliskRewardMessage($message)) {
                // Если это не сообщение о награде обелиска, показываем всем
                return true;
            }

            // Извлекаем информацию о расе из сообщения о награде
            $messageRace = $this->extractRaceFromObeliskMessage($message);

            // Если не удалось определить расу из сообщения, показываем всем
            if (!$messageRace) {
                return true;
            }

            // Показываем сообщение только игрокам союзных рас
            return $this->areAlliedRaces($messageRace, $userRace);
        }));
    }

    /**
     * Проверяет, является ли сообщение логом награды обелиска
     *
     * @param string $message Сообщение лога
     * @return bool
     */
    private function isObeliskRewardMessage(string $message): bool
    {
        // Ищем характерные признаки сообщений о наградах обелиска
        $obeliskPatterns = [
            'получил</span>', // Паттерн из formatObeliskRewardForOthers: "{$playerName} получил"
            'не получил</span>', // Паттерн из formatObeliskRewardFailureForOthers: "{$playerName} не получил"
            'Вы получили</span>', // Паттерн из formatObeliskRewardForSelf: "Вы получили"
            'Вы не получили</span>', // Паттерн из formatObeliskRewardFailureForSelf: "Вы не получили"
            'рюкзак полон' // Общий паттерн для неудачных попыток
        ];

        foreach ($obeliskPatterns as $pattern) {
            if (stripos($message, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Извлекает расу игрока из сообщения о награде обелиска
     *
     * @param string $message Сообщение лога
     * @return string|null Раса игрока или null, если не удалось определить
     */
    private function extractRaceFromObeliskMessage(string $message): ?string
    {
        // Проверяем, является ли это сообщением для самого игрока
        if (stripos($message, 'Вы получили</span>') !== false || stripos($message, 'Вы не получили</span>') !== false) {
            // Для сообщений "Вы получили/не получили" нужно получить расу текущего пользователя
            // Но поскольку мы уже знаем расу пользователя (она передается в метод фильтрации),
            // эти сообщения всегда должны показываться самому игроку
            return null; // Возвращаем null, чтобы показать сообщение
        }

        // Ищем имя игрока в HTML-сообщении для других игроков
        // Паттерн для поиска имени игрока в сообщениях типа "<span>Игрок получил</span>"
        if (preg_match('/>([^<>\s]+)\s+(получил|не получил)<\/span>/', $message, $matches)) {
            $playerName = $matches[1];

            // Ищем игрока в базе данных по имени
            $user = \App\Models\User::where('name', $playerName)->first();

            if ($user && $user->profile) {
                return $user->profile->race;
            }
        }

        return null;
    }

    /**
     * Проверяет, являются ли две расы союзными
     *
     * @param string $race1 Первая раса
     * @param string $race2 Вторая раса
     * @return bool true, если расы союзные
     */
    private function areAlliedRaces(string $race1, string $race2): bool
    {
        // В текущей системе игры есть две основные расы: solarius и lunarius
        // Они являются врагами друг другу, поэтому союзными считаются только одинаковые расы
        return strtolower($race1) === strtolower($race2);
    }

    /**
     * Логирование лечения бота-жреца с правильным разделением по типам локаций
     *
     * @param int $healerId ID бота-жреца
     * @param int $targetId ID цели лечения
     * @param string $targetType Тип цели (player/bot)
     * @param int $healAmount Количество восстановленного HP
     * @param int $oldHp Старое значение HP
     * @param int $newHp Новое значение HP
     * @param int|null $locationId ID локации
     */
    public function logBotHealing(int $healerId, int $targetId, string $targetType, int $healAmount, int $oldHp, int $newHp, ?int $locationId = null): void
    {
        try {
            $healer = \App\Models\Bot::find($healerId);
            if (!$healer) {
                Log::warning("BattleLogService: Бот-жрец не найден", ['healer_id' => $healerId]);
                return;
            }

            if ($targetType === 'player') {
                $target = \App\Models\User::find($targetId);
                if ($target) {
                    // Используем LogFormattingService для единообразного форматирования
                    $logFormattingService = app(\App\Services\LogFormattingService::class);
                    $healMessage = $logFormattingService->formatBotHeal($healer, $target, $healAmount, $oldHp, $newHp);

                    // ИСПРАВЛЕНИЕ: Определяем тип локации для выбора правильного ключа
                    $locationType = 'mine'; // По умолчанию
                    if ($locationId) {
                        $locationType = $this->determineLocationType($locationId, $healer->location);
                    }

                    // Выбираем ключ в зависимости от типа локации
                    if ($locationType === 'outpost') {
                        // Для аванпостов используем единый персональный ключ
                        $playerKey = $this->getBattleLogKey($targetId);
                        Log::debug("BattleLogService: Используем единый ключ для аванпоста", [
                            'location_type' => $locationType,
                            'player_key' => $playerKey,
                            'location_id' => $locationId
                        ]);
                    } else {
                        // Для рудников используем категоризированный ключ
                        $playerKey = "battle_logs:mines:{$targetId}";
                        Log::debug("BattleLogService: Используем категоризированный ключ для рудника", [
                            'location_type' => $locationType,
                            'player_key' => $playerKey,
                            'location_id' => $locationId
                        ]);
                    }

                    // Добавляем лог в персональный журнал игрока
                    $this->addLog($playerKey, $healMessage, 'success');

                    // Дополнительно добавляем в общий журнал локации (если указан ID локации)
                    if ($locationId) {
                        $locationKey = $locationType === 'outpost' ? "outpost:location:{$locationId}" : "mine:location:{$locationId}";
                        $generalMessage = "🩹 Жрец {$healer->name} вылечил {$target->name} на {$healAmount} HP";
                        $this->addLog($locationKey, $generalMessage, 'info');
                    }

                    Log::info("BattleLogService: Лог лечения бота добавлен с правильным разделением", [
                        'healer_id' => $healerId,
                        'healer_name' => $healer->name,
                        'healer_location' => $healer->location,
                        'target_id' => $targetId,
                        'target_name' => $target->name,
                        'heal_amount' => $healAmount,
                        'old_hp' => $oldHp,
                        'new_hp' => $newHp,
                        'player_key' => $playerKey,
                        'location_id' => $locationId,
                        'location_type' => $locationType
                    ]);
                } else {
                    Log::warning("BattleLogService: Цель лечения не найдена", [
                        'target_id' => $targetId,
                        'healer_id' => $healerId
                    ]);
                }
            } elseif ($targetType === 'bot') {
                $target = \App\Models\Bot::find($targetId);
                if ($target && $locationId) {
                    // Определяем тип локации для правильного ключа
                    $locationType = $this->determineLocationType($locationId, $healer->location);
                    $locationKey = $locationType === 'outpost' ? "outpost:location:{$locationId}" : "mine:location:{$locationId}";
                    $generalMessage = "🩹 Жрец {$healer->name} вылечил бота {$target->name} на {$healAmount} HP";
                    $this->addLog($locationKey, $generalMessage, 'info');

                    Log::info("BattleLogService: Лог лечения бота-бота добавлен", [
                        'healer_id' => $healerId,
                        'healer_name' => $healer->name,
                        'target_id' => $targetId,
                        'target_name' => $target->name,
                        'heal_amount' => $healAmount,
                        'location_id' => $locationId,
                        'location_type' => $locationType
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error("BattleLogService: Ошибка при логировании лечения", [
                'healer_id' => $healerId,
                'target_id' => $targetId,
                'target_type' => $targetType,
                'heal_amount' => $healAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Логирование смерти бота в рудниках
     *
     * @param int $botId ID бота
     * @param int|null $killerId ID убийцы
     * @param string $killerType Тип убийцы (player/bot)
     * @param int|null $locationId ID локации
     */
    public function logBotDeath(int $botId, ?int $killerId, string $killerType, ?int $locationId): void
    {
        try {
            $bot = \App\Models\Bot::find($botId);
            if (!$bot) {
                return;
            }

            $message = "💀 Бот {$bot->name} погиб";

            if ($killerId && $killerType === 'player') {
                $killer = \App\Models\User::find($killerId);
                if ($killer) {
                    $message .= " от руки игрока {$killer->name}";

                    // Добавляем лог в журнал убийцы
                    $killerKey = $this->getBattleLogKey($killerId);
                    $this->addLog($killerKey, "⚔️ Вы убили бота {$bot->name} в рудниках!", 'success');
                }
            }

            $message .= " в рудниках";

            // Добавляем лог в общий журнал локации
            if ($locationId) {
                $locationKey = "mine:location:{$locationId}";
                $this->addLog($locationKey, $message, 'info');
            }

        } catch (\Exception $e) {
            Log::error("Ошибка логирования смерти бота: " . $e->getMessage());
        }
    }

    /**
     * Логирование смерти игрока в рудниках
     *
     * @param int $playerId ID игрока
     * @param int|null $killerId ID убийцы
     * @param string $killerType Тип убийцы (bot/player)
     * @param int|null $locationId ID локации
     */
    public function logPlayerDeath(int $playerId, ?int $killerId, string $killerType, ?int $locationId): void
    {
        try {
            $player = \App\Models\User::find($playerId);
            if (!$player) {
                return;
            }

            $message = "💀 Игрок {$player->name} погиб";

            if ($killerId && $killerType === 'bot') {
                $killer = \App\Models\Bot::find($killerId);
                if ($killer) {
                    $message .= " от атаки бота {$killer->name}";
                }
            }

            $message .= " в рудниках";

            // Добавляем лог в журнал игрока
            $playerKey = $this->getBattleLogKey($playerId);
            $this->addLog($playerKey, "💀 Вы погибли в рудниках!", 'danger');

            // Добавляем лог в общий журнал локации
            if ($locationId) {
                $locationKey = "mine:location:{$locationId}";
                $this->addLog($locationKey, $message, 'info');
            }

        } catch (\Exception $e) {
            Log::error("Ошибка логирования смерти игрока: " . $e->getMessage());
        }
    }

    /**
     * Логирование атаки бота в рудниках
     *
     * @param int $botId ID бота
     * @param int $targetId ID цели
     * @param string $targetType Тип цели (player/bot)
     * @param int $damage Урон
     * @param int|null $locationId ID локации
     */
    public function logBotAttack(int $botId, int $targetId, string $targetType, int $damage, ?int $locationId): void
    {
        try {
            $bot = \App\Models\Bot::find($botId);
            if (!$bot) {
                Log::warning("BattleLogService: Бот не найден для логирования атаки", ['bot_id' => $botId]);
                return;
            }

            if ($targetType === 'player') {
                $target = \App\Models\User::find($targetId);
                if ($target) {
                    // ИСПРАВЛЕНО: Используем унифицированный ключ для рудников
                    $unifiedMineKey = "battle_logs:mines:{$targetId}";

                    // Форматируем сообщение с помощью LogFormattingService
                    $logFormattingService = app(\App\Services\LogFormattingService::class);
                    $attackMessage = $logFormattingService->formatBotAttack($bot, $target, $damage, false);

                    // Добавляем лог в унифицированный журнал рудников
                    $this->addLog($unifiedMineKey, $attackMessage, 'danger');

                    Log::info("BattleLogService: Лог атаки бота добавлен в унифицированный журнал", [
                        'bot_id' => $botId,
                        'bot_name' => $bot->name,
                        'target_id' => $targetId,
                        'target_name' => $target->name,
                        'damage' => $damage,
                        'unified_key' => $unifiedMineKey,
                        'location_id' => $locationId
                    ]);

                    // Дополнительно добавляем в общий журнал локации (если нужно)
                    if ($locationId) {
                        $locationKey = "mine:location:{$locationId}";
                        $generalMessage = "⚔️ Бот {$bot->name} атакует {$target->name} и наносит {$damage} урона";
                        $this->addLog($locationKey, $generalMessage, 'info');
                    }
                } else {
                    Log::warning("BattleLogService: Цель атаки не найдена", [
                        'target_id' => $targetId,
                        'bot_id' => $botId
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error("BattleLogService: Ошибка логирования атаки бота", [
                'error' => $e->getMessage(),
                'bot_id' => $botId,
                'target_id' => $targetId,
                'damage' => $damage
            ]);
        }
    }

    /**
     * Определяет тип локации (outpost или mine) по ID и названию
     * ИСПРАВЛЕНИЕ: Приоритет отдается проверке по названию, а не по ID
     *
     * @param int $locationId ID локации
     * @param string $locationName Название локации
     * @return string Тип локации ('outpost' или 'mine')
     */
    private function determineLocationType(int $locationId, string $locationName): string
    {
        try {
            // ИСПРАВЛЕНИЕ: Сначала проверяем по названию в аванпостах
            $outpostByName = \App\Models\Location::where('name', $locationName)
                ->where('location_type', 'outpost')
                ->where('is_active', true)
                ->first();

            if ($outpostByName) {
                Log::debug("BattleLogService: Локация определена как аванпост по названию", [
                    'location_id' => $locationId,
                    'location_name' => $locationName,
                    'outpost_id' => $outpostByName->id
                ]);
                return 'outpost';
            }

            // Проверяем по названию в рудниках
            $mineByName = \App\Models\MineLocation::where('name', $locationName)->first();
            if ($mineByName) {
                Log::debug("BattleLogService: Локация определена как рудник по названию", [
                    'location_id' => $locationId,
                    'location_name' => $locationName,
                    'mine_id' => $mineByName->id
                ]);
                return 'mine';
            }

            // Если не найдено по названию, проверяем по ID в таблице locations (аванпосты)
            $outpostLocation = \App\Models\Location::where('id', $locationId)
                ->where('location_type', 'outpost')
                ->where('is_active', true)
                ->first();

            if ($outpostLocation) {
                Log::debug("BattleLogService: Локация определена как аванпост по ID", [
                    'location_id' => $locationId,
                    'location_name' => $locationName,
                    'outpost_name' => $outpostLocation->name
                ]);
                return 'outpost';
            }

            // Проверяем по ID в таблице mine_locations (рудники)
            $mineLocation = \App\Models\MineLocation::find($locationId);
            if ($mineLocation) {
                Log::debug("BattleLogService: Локация определена как рудник по ID", [
                    'location_id' => $locationId,
                    'location_name' => $locationName,
                    'mine_name' => $mineLocation->name
                ]);
                return 'mine';
            }

            // По умолчанию считаем рудником
            Log::warning("BattleLogService: Не удалось определить тип локации, используем 'mine' по умолчанию", [
                'location_id' => $locationId,
                'location_name' => $locationName
            ]);
            return 'mine';

        } catch (\Exception $e) {
            Log::error("BattleLogService: Ошибка при определении типа локации", [
                'location_id' => $locationId,
                'location_name' => $locationName,
                'error' => $e->getMessage()
            ]);
            return 'mine'; // По умолчанию
        }
    }

    /**
     * Мигрирует логи из категоризированных ключей в единый персональный ключ игрока
     *
     * @param string $unifiedKey Единый персональный ключ (battle_logs:{user_id})
     */
    private function migrateToUnifiedBattleLogsIfNeeded(string $unifiedKey): void
    {
        // Извлекаем user_id из ключа
        if (!preg_match('/battle_logs:(\d+)/', $unifiedKey, $matches)) {
            return;
        }

        $userId = $matches[1];
        $unifiedRedisKey = "{$unifiedKey}:logs";

        // Проверяем, есть ли уже логи в новом ключе
        if (Redis::exists($unifiedRedisKey) > 0) {
            return; // Миграция уже выполнена
        }

        // Проверяем флаг миграции для этого пользователя
        $migrationFlag = "migration:unified_logs:{$userId}";
        if (Redis::exists($migrationFlag) > 0) {
            return; // Миграция уже была попытка
        }

        // Устанавливаем флаг миграции
        Redis::setex($migrationFlag, 3600, '1'); // Флаг на 1 час

        try {
            $allLogs = [];

            // Собираем логи из всех возможных источников
            $legacyKeys = [
                // Старые ключи рудников
                "battle_logs:mines:{$userId}:logs",
                "location:test_mine:{$userId}:logs",
                "location:iron_mine:{$userId}:logs",
                "location:coal_mine:{$userId}:logs",
                "location:gold_mine:{$userId}:logs",
                "location:diamond_mine:{$userId}:logs",
                "location:tarnmore_quarry:{$userId}:logs",

                // Старые ключи аванпостов (если есть)
                "battle_logs:{$userId}:Elven Haven:logs",
                "battle_logs:{$userId}:Dawn Fort:logs",
                "battle_logs:{$userId}:Sandy Stronghold:logs",
                "battle_logs:{$userId}:Ironhold Outpost:logs",
                "battle_logs:{$userId}:Moonlight Sanctuary:logs",
                "battle_logs:{$userId}:Shadowmere Outpost:logs",
            ];

            // Получаем все активные аванпосты для поиска дополнительных ключей
            $outposts = \App\Models\Location::where('location_type', 'outpost')
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();

            foreach ($outposts as $outpostName) {
                $legacyKeys[] = "battle_logs:{$userId}:{$outpostName}:logs";
            }

            // Собираем логи из всех старых ключей
            foreach ($legacyKeys as $legacyKey) {
                if (Redis::exists($legacyKey) > 0) {
                    $logs = Redis::lrange($legacyKey, 0, -1);
                    foreach ($logs as $log) {
                        $logData = json_decode($log, true);
                        if ($logData && isset($logData['created_at'])) {
                            $allLogs[] = $logData;
                        }
                    }

                    Log::info("Найдены логи для унификации", [
                        'legacy_key' => $legacyKey,
                        'logs_count' => count($logs),
                        'user_id' => $userId
                    ]);
                }
            }

            // Сортируем логи по времени создания (новые первыми)
            usort($allLogs, function ($a, $b) {
                return ($b['created_at'] ?? 0) - ($a['created_at'] ?? 0);
            });

            // Ограничиваем количество логов
            $allLogs = array_slice($allLogs, 0, self::MAX_LOGS);

            // Записываем в новый ключ
            if (!empty($allLogs)) {
                foreach ($allLogs as $log) {
                    Redis::lpush($unifiedRedisKey, json_encode($log));
                }
                Redis::expire($unifiedRedisKey, self::LOGS_TTL);

                Log::info("Миграция логов в единый журнал завершена", [
                    'user_id' => $userId,
                    'unified_key' => $unifiedRedisKey,
                    'migrated_logs' => count($allLogs),
                    'sources_checked' => count($legacyKeys)
                ]);

                // Удаляем старые ключи после успешной миграции
                foreach ($legacyKeys as $legacyKey) {
                    if (Redis::exists($legacyKey) > 0) {
                        Redis::del($legacyKey);
                        Log::debug("Удален старый ключ логов", [
                            'key' => $legacyKey,
                            'user_id' => $userId
                        ]);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("Ошибка миграции логов в единый журнал", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Мигрирует старые логи рудников в новый унифицированный ключ
     *
     * @param string $unifiedKey Новый унифицированный ключ (battle_logs:mines:{user_id})
     */
    private function migrateLegacyMineLogsIfNeeded(string $unifiedKey): void
    {
        // Извлекаем user_id из ключа
        if (!preg_match('/battle_logs:mines:(\d+)/', $unifiedKey, $matches)) {
            return;
        }

        $userId = $matches[1];
        $unifiedRedisKey = "{$unifiedKey}:logs";

        // Проверяем, есть ли уже логи в новом ключе
        if (Redis::exists($unifiedRedisKey) > 0) {
            return; // Миграция уже выполнена
        }

        // Проверяем флаг миграции для этого пользователя
        $migrationFlag = "migration:mine_logs:{$userId}";
        if (Redis::exists($migrationFlag) > 0) {
            return; // Миграция уже была попытка
        }

        // Устанавливаем флаг миграции
        Redis::setex($migrationFlag, 3600, '1'); // Флаг на 1 час

        try {
            $allLogs = [];
            $legacyKeys = [
                "location:test_mine:{$userId}:logs",
                "location:iron_mine:{$userId}:logs",
                "location:coal_mine:{$userId}:logs",
                "location:gold_mine:{$userId}:logs",
                "location:diamond_mine:{$userId}:logs",
                "location:tarnmore_quarry:{$userId}:logs"
            ];

            // Собираем логи из всех старых ключей
            foreach ($legacyKeys as $legacyKey) {
                if (Redis::exists($legacyKey) > 0) {
                    $logs = Redis::lrange($legacyKey, 0, -1);
                    foreach ($logs as $log) {
                        $logData = json_decode($log, true);
                        if ($logData && isset($logData['created_at'])) {
                            $allLogs[] = $logData;
                        }
                    }

                    Log::info("Найдены логи для миграции", [
                        'legacy_key' => $legacyKey,
                        'logs_count' => count($logs)
                    ]);
                }
            }

            // Сортируем логи по времени создания (новые первыми)
            usort($allLogs, function ($a, $b) {
                return ($b['created_at'] ?? 0) - ($a['created_at'] ?? 0);
            });

            // Ограничиваем количество логов
            $allLogs = array_slice($allLogs, 0, self::MAX_LOGS);

            // Записываем в новый ключ
            if (!empty($allLogs)) {
                foreach ($allLogs as $log) {
                    Redis::lpush($unifiedRedisKey, json_encode($log));
                }
                Redis::expire($unifiedRedisKey, self::LOGS_TTL);

                Log::info("Миграция логов рудников завершена", [
                    'user_id' => $userId,
                    'unified_key' => $unifiedRedisKey,
                    'migrated_logs' => count($allLogs)
                ]);

                // Удаляем старые ключи после успешной миграции
                foreach ($legacyKeys as $legacyKey) {
                    if (Redis::exists($legacyKey) > 0) {
                        Redis::del($legacyKey);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("Ошибка миграции логов рудников", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }
}