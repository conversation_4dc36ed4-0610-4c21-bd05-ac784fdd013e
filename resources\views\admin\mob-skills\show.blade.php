<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $mobSkill->name }} - Скилл моба - Админ панель</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        {{-- Заголовок страницы --}}
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">⚔️ {{ $mobSkill->name }}</h1>
                    <p class="text-[#998d66]">Детальная информация о скилле моба</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.mob-skills.index') }}" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к списку
                    </a>
                    <a href="{{ route('admin.mob-skills.edit', $mobSkill) }}" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ✏️ Редактировать
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {{-- Основная информация о скилле --}}
            <div class="lg:col-span-2">
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h2 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-2xl mr-3">📋</span>
                            Основная информация
                        </h2>
                    </div>
                    <div class="p-6 space-y-6">
                        {{-- Название и описание --}}
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Название скилла</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0]">
                                    {{ $mobSkill->name }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Иконка</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] flex items-center">
                                    @if($mobSkill->icon)
                                        <img src="{{ asset($mobSkill->icon) }}" alt="{{ $mobSkill->name }}" class="w-8 h-8 mr-3">
                                        <span class="text-sm text-[#998d66]">{{ $mobSkill->icon }}</span>
                                    @else
                                        <span class="text-[#998d66]">Не указана</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-[#c1a96e] mb-2">Описание</label>
                            <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-[#d4cbb0] min-h-[80px]">
                                {{ $mobSkill->description ?? 'Описание не указано' }}
                            </div>
                        </div>

                        {{-- Параметры скилла --}}
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Тип эффекта</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center">
                                    <span class="inline-block px-3 py-1 rounded-full text-sm font-medium
                                        @if($mobSkill->effect_type === 'stun') bg-red-900/30 text-red-300 border border-red-700
                                        @elseif($mobSkill->effect_type === 'buff') bg-green-900/30 text-green-300 border border-green-700
                                        @elseif($mobSkill->effect_type === 'debuff') bg-orange-900/30 text-orange-300 border border-orange-700
                                        @elseif($mobSkill->effect_type === 'dot') bg-purple-900/30 text-purple-300 border border-purple-700
                                        @elseif($mobSkill->effect_type === 'damage') bg-red-900/30 text-red-300 border border-red-700
                                        @else bg-gray-900/30 text-gray-300 border border-gray-700
                                        @endif">
                                        {{ ucfirst($mobSkill->effect_type) }}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Шанс (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->chance }}%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Кулдаун (сек)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->cooldown }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Длительность (сек)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->duration }}
                                </div>
                            </div>
                        </div>

                        {{-- Дополнительные параметры --}}
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Тип цели</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ ucfirst($mobSkill->target_type) }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Мин. HP (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->min_health_percent }}%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Макс. HP (%)</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->max_health_percent }}%
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">Приоритет</label>
                                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3 text-center text-[#d4cbb0]">
                                    {{ $mobSkill->priority }}
                                </div>
                            </div>
                        </div>

                        {{-- Данные эффекта --}}
                        @if($mobSkill->effect_data)
                        <div>
                            <label class="block text-sm font-medium text-[#c1a96e] mb-2">Данные эффекта</label>
                            <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg px-4 py-3">
                                <pre class="text-sm text-[#d4cbb0] whitespace-pre-wrap">{{ json_encode($mobSkill->effect_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                            </div>
                        </div>
                        @endif

                        {{-- Статус --}}
                        <div class="flex items-center justify-between pt-4 border-t border-[#3b3629]">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-[#c1a96e] mr-3">Статус:</span>
                                <span class="inline-block px-3 py-1 rounded-full text-sm font-medium
                                    @if($mobSkill->is_active) bg-green-900/30 text-green-300 border border-green-700
                                    @else bg-red-900/30 text-red-300 border border-red-700
                                    @endif">
                                    {{ $mobSkill->is_active ? 'Активен' : 'Неактивен' }}
                                </span>
                            </div>
                            <div class="text-sm text-[#998d66]">
                                Создан: {{ $mobSkill->created_at->format('d.m.Y H:i') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Боковая панель --}}
            <div class="space-y-6">
                {{-- Статистика --}}
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">📊</span>
                            Статистика
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Мобов с этим скиллом:</span>
                            <span class="text-[#c1a96e] font-semibold">{{ $mobsWithThisSkill->count() }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-[#998d66]">Всего привязок:</span>
                            <span class="text-[#c1a96e] font-semibold">{{ $mobSkill->mobSkills->count() }}</span>
                        </div>
                    </div>
                </div>

                {{-- Действия --}}
                <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                    <div class="px-6 py-4 border-b border-[#3b3629]">
                        <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                            <span class="text-xl mr-3">⚙️</span>
                            Действия
                        </h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('admin.mob-skills.edit', $mobSkill) }}" 
                           class="w-full bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#3b3629] transition duration-300 flex items-center justify-center">
                            ✏️ Редактировать скилл
                        </a>
                        <a href="{{ route('admin.mob-skills.assign', $mobSkill) }}" 
                           class="w-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] hover:from-[#4a452c] hover:to-[#3d3a2e] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#3b3629] transition duration-300 flex items-center justify-center">
                            🔗 Привязать к мобам
                        </a>
                        <form method="POST" action="{{ route('admin.mob-skills.destroy', $mobSkill) }}" 
                              onsubmit="return confirm('Вы уверены, что хотите удалить этот скилл? Это действие нельзя отменить.')" 
                              class="w-full">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full bg-gradient-to-b from-[#59372d] to-[#3c221b] hover:from-[#6e3f35] hover:to-[#4a2a20] text-[#f8eac2] px-4 py-3 rounded-lg border border-[#6e3f35] transition duration-300 flex items-center justify-center">
                                🗑️ Удалить скилл
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {{-- Список мобов, использующих этот скилл --}}
        @if($mobsWithThisSkill->count() > 0)
        <div class="mt-8">
            <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden">
                <div class="px-6 py-4 border-b border-[#3b3629]">
                    <h3 class="text-xl font-semibold text-[#e4d7b0] flex items-center">
                        <span class="text-2xl mr-3">👹</span>
                        Мобы, использующие этот скилл ({{ $mobsWithThisSkill->count() }})
                    </h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-[#1a1814] border-b border-[#3b3629]">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Моб</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Локация</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">HP</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Уровень</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-[#c1a96e] uppercase tracking-wider">Действия</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-[#3b3629]">
                            @foreach($mobsWithThisSkill as $mob)
                            <tr class="hover:bg-[#2a2722] transition duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        @if($mob->icon)
                                            <img src="{{ asset($mob->icon) }}" alt="{{ $mob->name }}" class="w-8 h-8 mr-3 rounded">
                                        @endif
                                        <div>
                                            <div class="text-sm font-medium text-[#d4cbb0]">{{ $mob->name }}</div>
                                            <div class="text-sm text-[#998d66]">ID: {{ $mob->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    {{ $mob->location ?? 'Не указана' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    {{ $mob->hp }}/{{ $mob->max_hp }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                    {{ $mob->strength + $mob->defense + $mob->agility + $mob->vitality + $mob->intelligence }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{{ route('admin.mobs.show', $mob) }}" 
                                       class="text-[#c1a96e] hover:text-[#e4d7b0] transition duration-200">
                                        Просмотр
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>
</body>
</html>
