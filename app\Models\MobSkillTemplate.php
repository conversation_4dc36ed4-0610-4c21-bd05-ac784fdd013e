<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MobSkillTemplate extends Model
{
    use HasFactory;

    protected $table = 'mob_skill_templates';

    protected $fillable = [
        'name',
        'description',
        'icon',
        'effect_type',
        'effect_data',
        'chance',
        'cooldown',
        'duration',
        'target_type',
        'min_health_percent',
        'max_health_percent',
        'is_active',
        'priority'
    ];

    protected $casts = [
        'effect_data' => 'array',
        'is_active' => 'boolean',
        'chance' => 'integer',
        'cooldown' => 'integer',
        'duration' => 'integer',
        'min_health_percent' => 'integer',
        'max_health_percent' => 'integer',
        'priority' => 'integer'
    ];

    /**
     * Связь с привязанными к мобам скиллами
     */
    public function mobSkills(): HasMany
    {
        return $this->hasMany(MobSkill::class, 'skill_template_id');
    }

    /**
     * Скоуп для активных шаблонов
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Скоуп для шаблонов по типу эффекта
     */
    public function scopeByEffectType($query, $effectType)
    {
        return $query->where('effect_type', $effectType);
    }

    /**
     * Скоуп для сортировки по приоритету
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Получить отформатированный шанс использования
     */
    public function getFormattedChanceAttribute()
    {
        return $this->chance . '%';
    }

    /**
     * Получить отформатированное время перезарядки
     */
    public function getFormattedCooldownAttribute()
    {
        if ($this->cooldown == 0) {
            return 'Без перезарядки';
        }
        
        return $this->cooldown . ' сек.';
    }

    /**
     * Получить отформатированную длительность эффекта
     */
    public function getFormattedDurationAttribute()
    {
        if ($this->duration == 0) {
            return 'Мгновенный';
        }
        
        return $this->duration . ' сек.';
    }

    /**
     * Получить название типа эффекта на русском
     */
    public function getEffectTypeNameAttribute()
    {
        $types = [
            'stun' => 'Оглушение',
            'damage' => 'Дополнительный урон',
            'heal' => 'Лечение',
            'buff' => 'Положительный эффект',
            'debuff' => 'Отрицательный эффект',
            'dot' => 'Урон со временем',
            'hot' => 'Лечение со временем',
            'teleport' => 'Телепортация',
            'summon' => 'Призыв',
            'special' => 'Специальный эффект'
        ];

        return $types[$this->effect_type] ?? $this->effect_type;
    }

    /**
     * Получить название типа цели на русском
     */
    public function getTargetTypeNameAttribute()
    {
        $types = [
            'player' => 'Игрок',
            'mob' => 'Моб',
            'self' => 'Себя',
            'area' => 'Область'
        ];

        return $types[$this->target_type] ?? $this->target_type;
    }

    /**
     * Проверить, может ли скилл быть использован при текущем проценте здоровья
     */
    public function canUseAtHealthPercent($healthPercent)
    {
        return $healthPercent >= $this->min_health_percent && 
               $healthPercent <= $this->max_health_percent;
    }

    /**
     * Получить CSS класс для иконки типа эффекта
     */
    public function getEffectTypeIconClassAttribute()
    {
        $classes = [
            'stun' => 'text-yellow-500',
            'damage' => 'text-red-500',
            'heal' => 'text-green-500',
            'buff' => 'text-blue-500',
            'debuff' => 'text-purple-500',
            'dot' => 'text-orange-500',
            'hot' => 'text-emerald-500',
            'teleport' => 'text-indigo-500',
            'summon' => 'text-pink-500',
            'special' => 'text-gray-500'
        ];

        return $classes[$this->effect_type] ?? 'text-gray-400';
    }

    /**
     * Получить эмодзи для типа эффекта
     */
    public function getEffectTypeEmojiAttribute()
    {
        $emojis = [
            'stun' => '⚡',
            'damage' => '⚔️',
            'heal' => '💚',
            'buff' => '🛡️',
            'debuff' => '💀',
            'dot' => '🔥',
            'hot' => '✨',
            'teleport' => '🌀',
            'summon' => '👥',
            'special' => '🎯'
        ];

        return $emojis[$this->effect_type] ?? '❓';
    }
}
