<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\ActiveEffect;
use App\Services\SkillService;
use App\Services\BattleLogService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Redis;

class MobSkillSystemTest extends TestCase
{
    use WithFaker;

    protected $skillService;
    protected $battleLogService;
    protected $testUser;
    protected $testMob;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->skillService = app(SkillService::class);
        $this->battleLogService = app(BattleLogService::class);
        
        // Создаем тестового пользователя
        $this->testUser = User::factory()->create([
            'name' => 'TestPlayer',
            'email' => '<EMAIL>'
        ]);
        
        // Создаем профиль пользователя если его нет
        if (!$this->testUser->profile) {
            $this->testUser->profile()->create([
                'hp' => 1000,
                'max_hp' => 1000,
                'mp' => 500,
                'max_mp' => 500,
                'strength' => 50,
                'defense' => 30,
                'agility' => 40,
                'vitality' => 35,
                'intelligence' => 25
            ]);
        }
        
        // Создаем тестового моба
        $this->testMob = Mob::create([
            'name' => 'Тестовый Орк',
            'hp' => 800,
            'max_hp' => 800,
            'strength' => 60,
            'defense' => 40,
            'agility' => 30,
            'vitality' => 50,
            'intelligence' => 20,
            'location' => 'test_location',
            'experience_reward' => 100
        ]);
    }

    protected function tearDown(): void
    {
        // Очищаем тестовые данные
        ActiveEffect::where('target_id', $this->testUser->id)->delete();
        ActiveEffect::where('target_id', $this->testMob->id)->delete();
        MobSkill::where('mob_id', $this->testMob->id)->delete();
        MobSkillTemplate::where('name', 'LIKE', 'Тест%')->delete();
        
        parent::tearDown();
    }

    /** @test */
    public function test_can_create_mob_skill_template()
    {
        $templateData = [
            'name' => 'Тестовый Удар',
            'description' => 'Тестовое описание скилла',
            'icon' => 'assets/skills/test.png',
            'effect_type' => 'damage',
            'effect_data' => [
                'damage' => 100,
                'message' => 'Получен урон от тестового удара!'
            ],
            'chance' => 30,
            'cooldown' => 15,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 5
        ];

        $template = MobSkillTemplate::create($templateData);

        $this->assertInstanceOf(MobSkillTemplate::class, $template);
        $this->assertEquals('Тестовый Удар', $template->name);
        $this->assertEquals('damage', $template->effect_type);
        $this->assertEquals(30, $template->chance);
        $this->assertTrue($template->is_active);
        
        echo "✅ Тест создания шаблона скилла пройден\n";
    }

    /** @test */
    public function test_can_assign_skill_to_mob()
    {
        // Создаем шаблон скилла
        $template = MobSkillTemplate::create([
            'name' => 'Тестовое Оглушение',
            'description' => 'Оглушает цель на несколько секунд',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 5,
                'disable_skills' => true,
                'message' => 'Вы оглушены!'
            ],
            'chance' => 25,
            'cooldown' => 20,
            'duration' => 5,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 7
        ]);

        // Привязываем скилл к мобу
        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id,
            'chance' => 35 // Переопределяем шанс
        ]);

        $this->assertInstanceOf(MobSkill::class, $mobSkill);
        $this->assertEquals($this->testMob->id, $mobSkill->mob_id);
        $this->assertEquals($template->id, $mobSkill->skill_template_id);
        $this->assertEquals(35, $mobSkill->chance);
        
        // Проверяем связи
        $this->assertInstanceOf(Mob::class, $mobSkill->mob);
        $this->assertInstanceOf(MobSkillTemplate::class, $mobSkill->skillTemplate);
        
        echo "✅ Тест привязки скилла к мобу пройден\n";
    }

    /** @test */
    public function test_mob_skill_stun_effect()
    {
        // Создаем шаблон стана
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Стан',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 10,
                'disable_skills' => true,
                'disable_movement' => true,
                'message' => 'Вы оглушены тестовым скиллом!'
            ],
            'chance' => 100, // 100% для теста
            'cooldown' => 30,
            'duration' => 10,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 8
        ]);

        // Привязываем к мобу
        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Обрабатываем скиллы моба
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('stun', $results[0]['effect_type']);

        // Проверяем, что создался активный эффект
        $activeEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('caster_type', 'mob')
            ->where('caster_id', $this->testMob->id)
            ->first();

        $this->assertNotNull($activeEffect);
        $this->assertEquals('stun', $activeEffect->effect_data['type']);
        
        echo "✅ Тест эффекта оглушения пройден\n";
    }

    /** @test */
    public function test_mob_skill_damage_effect()
    {
        $initialHp = $this->testUser->profile->hp;
        
        // Создаем шаблон урона
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Урон',
            'effect_type' => 'damage',
            'effect_data' => [
                'damage' => 150,
                'message' => 'Получен урон от тестового скилла!'
            ],
            'chance' => 100,
            'cooldown' => 10,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 6
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Обрабатываем скиллы моба
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('damage', $results[0]['effect_type']);
        $this->assertEquals(150, $results[0]['damage']);

        // Проверяем, что HP уменьшилось
        $this->testUser->profile->refresh();
        $this->assertEquals($initialHp - 150, $this->testUser->profile->hp);
        
        echo "✅ Тест эффекта урона пройден\n";
    }

    /** @test */
    public function test_mob_skill_dot_effect()
    {
        // Создаем шаблон DOT
        $template = MobSkillTemplate::create([
            'name' => 'Тестовое Кровотечение',
            'effect_type' => 'dot',
            'effect_data' => [
                'damage_per_tick' => 20,
                'tick_interval' => 2,
                'total_duration' => 10,
                'message' => 'Вы истекаете кровью!'
            ],
            'chance' => 100,
            'cooldown' => 25,
            'duration' => 10,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 4
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Обрабатываем скиллы моба
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('dot', $results[0]['effect_type']);

        // Проверяем активный эффект
        $activeEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('caster_type', 'mob')
            ->where('caster_id', $this->testMob->id)
            ->first();

        $this->assertNotNull($activeEffect);
        $this->assertEquals('dot', $activeEffect->effect_data['type']);
        $this->assertEquals(20, $activeEffect->effect_data['damage_per_tick']);
        
        echo "✅ Тест эффекта DOT пройден\n";
    }

    /** @test */
    public function test_mob_skill_cooldown_system()
    {
        // Создаем шаблон с коротким кулдауном
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Кулдаун',
            'effect_type' => 'damage',
            'effect_data' => ['damage' => 50],
            'chance' => 100,
            'cooldown' => 5,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 5
        ]);

        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Первое использование должно сработать
        $results1 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        $this->assertNotEmpty($results1);
        $this->assertTrue($results1[0]['success']);

        // Проверяем, что кулдаун установлен
        $mobSkill->refresh();
        $this->assertNotNull($mobSkill->cooldown_ends_at);
        $this->assertNotNull($mobSkill->last_used_at);

        // Второе использование сразу не должно сработать
        $results2 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        $this->assertEmpty($results2);
        
        echo "✅ Тест системы кулдаунов пройден\n";
    }

    /** @test */
    public function test_mob_skill_health_conditions()
    {
        // Создаем скилл, который работает только при низком HP
        $template = MobSkillTemplate::create([
            'name' => 'Тестовая Ярость',
            'effect_type' => 'buff',
            'effect_data' => [
                'damage_multiplier' => 1.5,
                'duration' => 15
            ],
            'chance' => 100,
            'cooldown' => 30,
            'duration' => 15,
            'target_type' => 'self',
            'min_health_percent' => 0,
            'max_health_percent' => 30, // Только при HP <= 30%
            'is_active' => true,
            'priority' => 9
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // При полном HP скилл не должен сработать
        $results1 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        $this->assertEmpty($results1);

        // Уменьшаем HP моба до 25%
        $this->testMob->hp = (int)($this->testMob->max_hp * 0.25);
        $this->testMob->save();

        // Теперь скилл должен сработать
        $results2 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        $this->assertNotEmpty($results2);
        $this->assertTrue($results2[0]['success']);
        $this->assertEquals('buff', $results2[0]['effect_type']);
        
        echo "✅ Тест условий здоровья пройден\n";
    }

    /** @test */
    public function test_battle_log_integration()
    {
        // Очищаем Redis для чистого теста
        Redis::flushall();
        
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Лог',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 3,
                'message' => 'Тестовое оглушение!'
            ],
            'chance' => 100,
            'cooldown' => 10,
            'duration' => 3,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 7
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Тестируем логирование
        $this->battleLogService->logMobSkillUsage(
            $this->testMob->id,
            $this->testMob->name,
            $this->testUser->id,
            $this->testUser->name,
            $template->name,
            $template->effect_type,
            $template->effect_data,
            'test_location'
        );

        // Проверяем, что лог добавился
        $battleLogKey = "location:test_location:{$this->testUser->id}";
        $logs = $this->battleLogService->getLogs($battleLogKey);
        
        $this->assertNotEmpty($logs);
        $this->assertStringContainsString('оглушил', $logs[0]['message']);
        $this->assertEquals('danger', $logs[0]['type']);
        
        echo "✅ Тест интеграции с боевыми логами пройден\n";
    }

    public function test_all_mob_skill_system()
    {
        echo "\n🧪 Запуск полного тестирования системы скиллов мобов...\n\n";
        
        $this->test_can_create_mob_skill_template();
        $this->test_can_assign_skill_to_mob();
        $this->test_mob_skill_stun_effect();
        $this->test_mob_skill_damage_effect();
        $this->test_mob_skill_dot_effect();
        $this->test_mob_skill_cooldown_system();
        $this->test_mob_skill_health_conditions();
        $this->test_battle_log_integration();
        
        echo "\n🎉 Все тесты системы скиллов мобов успешно пройдены!\n";
        echo "📊 Статистика:\n";
        echo "   - Создано шаблонов скиллов: " . MobSkillTemplate::where('name', 'LIKE', 'Тест%')->count() . "\n";
        echo "   - Создано привязок к мобам: " . MobSkill::where('mob_id', $this->testMob->id)->count() . "\n";
        echo "   - Создано активных эффектов: " . ActiveEffect::where('target_id', $this->testUser->id)->count() . "\n";
        echo "\n";
    }
}
