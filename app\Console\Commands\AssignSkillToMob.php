<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;

class AssignSkillToMob extends Command
{
    protected $signature = 'mob:assign-skill {mob_id} {skill_name} {--chance=25}';
    protected $description = 'Привязать скилл к мобу';

    public function handle()
    {
        $mobId = $this->argument('mob_id');
        $skillName = $this->argument('skill_name');
        $chance = $this->option('chance');

        $mob = Mob::find($mobId);
        if (!$mob) {
            $this->error("Моб с ID {$mobId} не найден!");
            return 1;
        }

        $template = MobSkillTemplate::where('name', $skillName)->first();
        if (!$template) {
            $this->error("Шаблон скилла '{$skillName}' не найден!");
            return 1;
        }

        // Проверяем, не привязан ли уже этот скилл
        $existing = MobSkill::where('mob_id', $mobId)
            ->where('skill_template_id', $template->id)
            ->first();

        if ($existing) {
            $this->warn("Скилл '{$skillName}' уже привязан к мобу '{$mob->name}' с шансом {$existing->chance}%");
            return 0;
        }

        // Создаем привязку
        MobSkill::create([
            'mob_id' => $mobId,
            'skill_template_id' => $template->id,
            'chance' => $chance
        ]);

        $this->info("✅ Скилл '{$skillName}' успешно привязан к мобу '{$mob->name}' с шансом {$chance}%");
        return 0;
    }
}
