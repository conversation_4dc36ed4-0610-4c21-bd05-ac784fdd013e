<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MobSkillTemplate;
use App\Models\Mob;
use App\Models\MobSkill;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class MobSkillController extends Controller
{
    /**
     * Главная страница управления скиллами мобов
     */
    public function index()
    {
        $templates = MobSkillTemplate::with('mobSkills.mob')
            ->orderBy('priority', 'desc')
            ->orderBy('name')
            ->paginate(15);

        $stats = [
            'total_templates' => MobSkillTemplate::count(),
            'active_templates' => MobSkillTemplate::active()->count(),
            'total_mob_skills' => MobSkill::count(),
            'mobs_with_skills' => Mob::whereHas('skills')->count(),
        ];

        return view('admin.mob-skills.index', compact('templates', 'stats'));
    }

    /**
     * Страница создания нового шаблона скилла
     */
    public function create()
    {
        $effectTypes = $this->getEffectTypes();
        $targetTypes = $this->getTargetTypes();
        $icons = $this->getAvailableIcons();

        return view('admin.mob-skills.create', compact('effectTypes', 'targetTypes', 'icons'));
    }

    /**
     * Сохранение нового шаблона скилла
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string',
            'effect_type' => 'required|in:stun,damage,heal,buff,debuff,dot,hot,teleport,summon,special',
            'effect_data' => 'nullable|json',
            'chance' => 'required|integer|min:1|max:100',
            'cooldown' => 'required|integer|min:0',
            'duration' => 'required|integer|min:0',
            'target_type' => 'required|in:player,mob,self,area',
            'min_health_percent' => 'required|integer|min:0|max:100',
            'max_health_percent' => 'required|integer|min:0|max:100',
            'priority' => 'required|integer|min:1|max:10',
            'is_active' => 'boolean'
        ]);

        // Декодируем effect_data если передан как строка
        if (isset($validated['effect_data']) && is_string($validated['effect_data'])) {
            $validated['effect_data'] = json_decode($validated['effect_data'], true);
        }

        $template = MobSkillTemplate::create($validated);

        return redirect()->route('admin.mob-skills.show', $template)
            ->with('success', 'Шаблон скилла успешно создан!');
    }

    /**
     * Просмотр шаблона скилла
     */
    public function show(MobSkillTemplate $mobSkill)
    {
        $mobSkill->load(['mobSkills.mob']);
        
        $mobsWithThisSkill = $mobSkill->mobSkills()
            ->with('mob')
            ->get()
            ->pluck('mob')
            ->unique('id');

        return view('admin.mob-skills.show', compact('mobSkill', 'mobsWithThisSkill'));
    }

    /**
     * Страница редактирования шаблона скилла
     */
    public function edit(MobSkillTemplate $mobSkill)
    {
        $effectTypes = $this->getEffectTypes();
        $targetTypes = $this->getTargetTypes();
        $icons = $this->getAvailableIcons();

        return view('admin.mob-skills.edit', compact('mobSkill', 'effectTypes', 'targetTypes', 'icons'));
    }

    /**
     * Обновление шаблона скилла
     */
    public function update(Request $request, MobSkillTemplate $mobSkill)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string',
            'effect_type' => 'required|in:stun,damage,heal,buff,debuff,dot,hot,teleport,summon,special',
            'effect_data' => 'nullable|json',
            'chance' => 'required|integer|min:1|max:100',
            'cooldown' => 'required|integer|min:0',
            'duration' => 'required|integer|min:0',
            'target_type' => 'required|in:player,mob,self,area',
            'min_health_percent' => 'required|integer|min:0|max:100',
            'max_health_percent' => 'required|integer|min:0|max:100',
            'priority' => 'required|integer|min:1|max:10',
            'is_active' => 'boolean'
        ]);

        // Декодируем effect_data если передан как строка
        if (isset($validated['effect_data']) && is_string($validated['effect_data'])) {
            $validated['effect_data'] = json_decode($validated['effect_data'], true);
        }

        $mobSkill->update($validated);

        return redirect()->route('admin.mob-skills.show', $mobSkill)
            ->with('success', 'Шаблон скилла успешно обновлен!');
    }

    /**
     * Удаление шаблона скилла
     */
    public function destroy(MobSkillTemplate $mobSkill)
    {
        $name = $mobSkill->name;
        $mobSkill->delete();

        return redirect()->route('admin.mob-skills.index')
            ->with('success', "Шаблон скилла '{$name}' успешно удален!");
    }

    /**
     * Страница привязки скиллов к мобу
     */
    public function assignToMob(Request $request)
    {
        $mobId = $request->get('mob_id');
        $mob = $mobId ? Mob::with('skills.skillTemplate')->findOrFail($mobId) : null;
        
        $mobs = Mob::orderBy('name')->get();
        $templates = MobSkillTemplate::active()->orderBy('priority', 'desc')->get();

        return view('admin.mob-skills.assign', compact('mob', 'mobs', 'templates'));
    }

    /**
     * Привязка скилла к мобу
     */
    public function attachToMob(Request $request)
    {
        $validated = $request->validate([
            'mob_id' => 'required|exists:mobs,id',
            'skill_template_id' => 'required|exists:mob_skill_templates,id',
            'chance' => 'nullable|integer|min:1|max:100'
        ]);

        $mob = Mob::findOrFail($validated['mob_id']);
        $template = MobSkillTemplate::findOrFail($validated['skill_template_id']);

        // Проверяем, не привязан ли уже этот скилл к мобу
        $existingSkill = MobSkill::where('mob_id', $mob->id)
            ->where('skill_template_id', $template->id)
            ->first();

        if ($existingSkill) {
            return back()->with('error', 'Этот скилл уже привязан к данному мобу!');
        }

        // Создаем привязку
        MobSkill::create([
            'mob_id' => $mob->id,
            'skill_template_id' => $template->id,
            'chance' => $validated['chance'] ?? $template->chance
        ]);

        return back()->with('success', "Скилл '{$template->name}' успешно привязан к мобу '{$mob->name}'!");
    }

    /**
     * Отвязка скилла от моба
     */
    public function detachFromMob(Request $request)
    {
        $validated = $request->validate([
            'mob_skill_id' => 'required|exists:mob_skills,id'
        ]);

        $mobSkill = MobSkill::with(['mob', 'skillTemplate'])->findOrFail($validated['mob_skill_id']);
        $mobName = $mobSkill->mob->name;
        $skillName = $mobSkill->skillTemplate->name ?? 'Неизвестный скилл';
        
        $mobSkill->delete();

        return back()->with('success', "Скилл '{$skillName}' отвязан от моба '{$mobName}'!");
    }

    /**
     * Получить список типов эффектов
     */
    private function getEffectTypes()
    {
        return [
            'stun' => 'Оглушение',
            'damage' => 'Дополнительный урон',
            'heal' => 'Лечение',
            'buff' => 'Положительный эффект',
            'debuff' => 'Отрицательный эффект',
            'dot' => 'Урон со временем',
            'hot' => 'Лечение со временем',
            'teleport' => 'Телепортация',
            'summon' => 'Призыв',
            'special' => 'Специальный эффект'
        ];
    }

    /**
     * Получить список типов целей
     */
    private function getTargetTypes()
    {
        return [
            'player' => 'Игрок',
            'mob' => 'Моб',
            'self' => 'Себя',
            'area' => 'Область'
        ];
    }

    /**
     * Получить список доступных иконок
     */
    private function getAvailableIcons()
    {
        $iconsPath = public_path('assets/skills/mobs');
        
        if (!File::exists($iconsPath)) {
            File::makeDirectory($iconsPath, 0755, true);
            return [];
        }

        $files = File::files($iconsPath);
        $icons = [];

        foreach ($files as $file) {
            if (in_array($file->getExtension(), ['png', 'jpg', 'jpeg', 'gif', 'svg'])) {
                $relativePath = 'assets/skills/mobs/' . $file->getFilename();
                $icons[] = $relativePath;
            }
        }

        return $icons;
    }
}
