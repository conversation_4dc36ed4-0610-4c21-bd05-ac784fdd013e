=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 5, <PERSON><PERSON>: 3
Ботов с выбранными целями: 8
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Золото-мудрость-800 (solarius) атакует player: battleTest
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Луна-жезл-560 (lunarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: battleTest
  Бот Свет-клинок-95 (solarius) атакует player: battleTest
  Бот Тень-маг-399 (lunarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Луна-жезл-560 (lunarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: battleTest
  Бот Тень-маг-399 (lunarius) атакует player: battleTest
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Свет-клинок-95 (lunarius) атакует player: battleTest
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Джошуа (lunarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: battleTest
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Луна-жезл-560 (lunarius) атакует player: battleTest
  Бот Свет-клинок-95 (lunarius) атакует player: battleTest
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Джошуа (lunarius) атакует player: battleTest
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Луна-жезл-560 (lunarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: battleTest
  Бот Свет-клинок-95 (lunarius) атакует player: battleTest
  Бот Джошуа (lunarius) атакует player: battleTest
  Бот Тень-маг-399 (lunarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: battleTest
  Бот Аура-честь-903 (solarius) атакует player: battleTest
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: battleTest
  Бот Луна-жезл-560 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: Player
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: Player
  Бот Сияние-маг-177 (solarius) атакует player: Player
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: Player
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 8
Активных ботов: 8, неактивных: 0
Ботов по фракциям: Solarius: 3, Lunarius: 5
Ботов с выбранными целями: 8
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует : цель не найдена
  Бот Луна-жезл-560 (lunarius) атакует : цель не найдена
  Бот Свет-клинок-95 (lunarius) атакует : цель не найдена
  Бот Ночь-мудрость-562 (lunarius) атакует : цель не найдена
  Бот Сияние-маг-177 (solarius) атакует : цель не найдена
  Бот Аура-честь-903 (solarius) атакует : цель не найдена
  Бот Тень-маг-399 (lunarius) атакует : цель не найдена
  Бот Джошуа (lunarius) атакует : цель не найдена
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 9
Активных ботов: 9, неактивных: 0
Ботов по фракциям: Solarius: 4, Lunarius: 5
Ботов с выбранными целями: 9
Информация о целях:
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 9
Активных ботов: 9, неактивных: 0
Ботов по фракциям: Solarius: 4, Lunarius: 5
Ботов с выбранными целями: 9
Информация о целях:
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Сияние-маг-177 (solarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Джошуа (lunarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Ночь-мудрость-562 (lunarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-копьё-976 (solarius) атакует player: Player
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: Player
  Бот Солнце-посох-751 (solarius) атакует player: Player
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Аура-молитва-999 (solarius) атакует player: Player
  Бот Луна-жезл-560 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 6
Информация о целях:
  Бот Солнце-посох-751 (solarius) атакует player: Player
  Бот Сияние-маг-177 (solarius) атакует player: Player
  Бот Аура-копьё-976 (solarius) атакует player: Player
  Бот Аура-молитва-999 (solarius) атакует player: Player
  Бот Аура-честь-903 (solarius) атакует player: Player
  Бот Луна-жезл-560 (lunarius) атакует player: Player
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 8, неактивных: 2
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует bot: Сияние-маг-177
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 5
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 5
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 5
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 5
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: admin
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Джошуа (lunarius) атакует player: admin
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Аура-честь-903 (solarius) атакует player: test
  Бот Тень-маг-399 (lunarius) атакует player: admin
  Бот Луна-жезл-560 (lunarius) атакует player: admin
  Бот Аура-копьё-976 (solarius) атакует player: test
  Бот Свет-клинок-95 (lunarius) атакует player: admin
  Бот Аура-молитва-999 (solarius) атакует player: test
  Бот Солнце-посох-751 (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: admin
  Бот Сияние-маг-177 (solarius) атакует player: test
  Бот Ночь-мудрость-562 (lunarius) атакует player: admin
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Эстен (solarius) атакует player: test
  Бот Чарлз (solarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Хагрин (solarius) атакует player: Alchim
  Бот Торвин (solarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Эстен (solarius) атакует player: test
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Чарлз (solarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Торвин (solarius) атакует player: Alchim
  Бот Хагрин (solarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Эстен (solarius) атакует player: test
  Бот Эшлин (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эстен (solarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Эстен (solarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Эшлин (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Эстен (solarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эстен (solarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 7
Информация о целях:
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Эстен (solarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Чарлз (solarius) атакует player: Alchim
  Бот Торвин (solarius) атакует player: Alchim
  Бот Хагрин (solarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Ниил (solarius) атакует player: test
  Бот Эстен (solarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis
=== Диагностика автоатаки ботов ===
Локация: Эльфийская Гавань
Всего ботов в локации: 10
Активных ботов: 10, неактивных: 0
Ботов по фракциям: Solarius: 5, Lunarius: 5
Ботов с выбранными целями: 10
Информация о целях:
  Бот Ниил (solarius) атакует player: test
  Бот Кейли (lunarius) атакует player: Alchim
  Бот Эшлин (lunarius) атакует player: Alchim
  Бот Хагрин (solarius) атакует player: Alchim
  Бот Рианна (lunarius) атакует player: Alchim
  Бот Торвин (solarius) атакует player: Alchim
  Бот Эстен (solarius) атакует player: Alchim
  Бот Джошуа (lunarius) атакует player: Alchim
  Бот Эндрю (lunarius) атакует player: Alchim
  Бот Чарлз (solarius) атакует player: Alchim
Ботов с недавними атаками (последний час): 0
Нет недавних атак. Возможно, автоатака не запускается или не выполняется.

 Запустить тестовую атаку для одного бота? (yes/no) [no]:
 > === Настройки очередей ===
Текущий драйвер очередей: redis

   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  


   ERROR  Command "bots:diagnose" is not defined. Did you mean one of these?  

  ⇂ bots:auto-attack  
  ⇂ bots:check  
  ⇂ bots:check-and-reset  
  ⇂ bots:check-dead  
  ⇂ bots:fix-damage  
  ⇂ bots:fix-dawn-fort  
  ⇂ bots:fix-targeting  
  ⇂ bots:fix-zero-hp  
  ⇂ bots:manage-location  
  ⇂ bots:process  
  ⇂ bots:test-attack  
  ⇂ bots:test-respawn  
  ⇂ bots:test-respawn-system  
  ⇂ bots:test-targeting  

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Доступные локации аванпостов:
- Тестовый Аванпост (ID: 1)

 Введите название локации для диагностики:
 > 
            
  Aborted.  
            

Запуск диагностики системы ботов
🔍 === ДИАГНОСТИКА СИСТЕМЫ БОТОВ ===

1️⃣ Проверка Redis...
  ✅ Redis доступен
  📊 Ключей ботов в Redis: 1
  👥 Ключей игроков в Redis: 2
  ⚙️ Конфигураций в Redis: 1
2️⃣ Проверка базы данных...
  ✅ База данных доступна
  🏭 Активных локаций рудников: 3
3️⃣ Проверка состояния ботов...
  🤖 Всего ботов: 19
  ✅ Активных: 19
  💀 Мертвых: 0
  👑 Созданных админом: 0
4️⃣ Проверка игроков в локациях...
  👥 Всего игроков: 7
  🟢 Онлайн: 1
5️⃣ Проверка очередей...
  📋 Очередей в Redis: 9
6️⃣ Проверка планировщика...
  ⏰ Проверка планировщика...


⚠️ === НАЙДЕНЫ ПРОБЛЕМЫ ===

⚠️ ПРЕДУПРЕЖДЕНИЯ:
  ⚠️  Планировщик не работал последние 5 минут
     💡 Решение: Проверьте: php artisan schedule:work или настройте cron

💡 Для автоматического исправления запустите с флагом --fix
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 1
✅ Активных: 1
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 1
✅ Активных: 1
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 10
✅ Активных: 10
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 23
✅ Активных: 23
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 23
✅ Активных: 23
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 26
✅ Активных: 26
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 26
✅ Активных: 26
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 27
✅ Активных: 27
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 31
✅ Активных: 31
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 33
✅ Активных: 33
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 33
✅ Активных: 33
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 33
✅ Активных: 33
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 34
✅ Активных: 34
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 35
✅ Активных: 35
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 1
✅ Активных: 1
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 1
✅ Активных: 1
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 1
✅ Активных: 1
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 0
✅ Активных: 0
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 43
✅ Активных: 43
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 43
✅ Активных: 43
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 69
✅ Активных: 69
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 44
✅ Активных: 44
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 54
✅ Активных: 54
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 54
✅ Активных: 54
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 54
✅ Активных: 54
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 54
✅ Активных: 54
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 54
✅ Активных: 54
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 55
✅ Активных: 55
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 57
✅ Активных: 57
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
🔍 Запуск диагностики системы ботов...
🤖 Всего ботов: 58
✅ Активных: 40
💀 Мертвых: 0
✅ Диагностика завершена
