<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\ActiveEffect;
use App\Services\SkillService;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Redis;

class TestMobSkillSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mob-skills {--quick : Запустить только быстрые тесты}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = 'Тестирование системы скиллов мобов';

    protected $skillService;
    protected $battleLogService;
    protected $testUser;
    protected $testMob;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Запуск тестирования системы скиллов мобов...');
        $this->newLine();

        try {
            $this->setUp();

            if ($this->option('quick')) {
                $this->info('⚡ Режим быстрого тестирования');
                $this->runQuickTests();
            } else {
                $this->info('🔍 Полное тестирование системы');
                $this->runFullTests();
            }

            $this->tearDown();

            $this->newLine();
            $this->info('✅ Тестирование завершено успешно!');

        } catch (\Exception $e) {
            $this->error('❌ Ошибка при тестировании: ' . $e->getMessage());
            $this->error('Стек вызовов: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    protected function setUp(): void
    {
        $this->skillService = app(SkillService::class);
        $this->battleLogService = app(BattleLogService::class);

        // Создаем тестового пользователя
        $this->testUser = User::where('email', '<EMAIL>')->first();
        if (!$this->testUser) {
            $this->testUser = User::create([
                'name' => 'TestPlayer',
                'email' => '<EMAIL>',
                'password' => bcrypt('password')
            ]);
        }

        // Создаем профиль пользователя если его нет
        if (!$this->testUser->profile) {
            $this->testUser->profile()->create([
                'hp' => 1000,
                'max_hp' => 1000,
                'mp' => 500,
                'max_mp' => 500,
                'strength' => 50,
                'defense' => 30,
                'agility' => 40,
                'vitality' => 35,
                'intelligence' => 25
            ]);
        }

        // Создаем тестового моба
        $this->testMob = Mob::where('name', 'Тестовый Орк')->first();
        if (!$this->testMob) {
            $this->testMob = Mob::create([
                'name' => 'Тестовый Орк',
                'slug' => 'testovyy-ork',
                'hp' => 800,
                'max_hp' => 800,
                'strength' => 60,
                'defense' => 40,
                'agility' => 30,
                'vitality' => 50,
                'intelligence' => 20,
                'location' => 'test_location',
                'experience_reward' => 100
            ]);
        }
    }

    protected function tearDown(): void
    {
        // Очищаем тестовые данные
        ActiveEffect::where('target_id', $this->testUser->id)->delete();
        ActiveEffect::where('target_id', $this->testMob->id)->delete();
        MobSkill::where('mob_id', $this->testMob->id)->delete();
        MobSkillTemplate::where('name', 'LIKE', 'Тест%')->delete();
    }

    /**
     * Запуск быстрых тестов
     */
    protected function runQuickTests()
    {
        $this->info('1. Тестирование создания шаблонов...');
        $this->testCreateMobSkillTemplate();

        $this->info('2. Тестирование привязки к мобам...');
        $this->testAssignSkillToMob();

        $this->info('3. Тестирование базовых эффектов...');
        $this->testMobSkillDamageEffect();

        $this->info('✅ Быстрые тесты пройдены!');
    }

    /**
     * Запуск полного набора тестов
     */
    protected function runFullTests()
    {
        $tests = [
            'testCreateMobSkillTemplate' => 'Создание шаблонов скиллов',
            'testAssignSkillToMob' => 'Привязка скиллов к мобам',
            'testMobSkillStunEffect' => 'Эффект оглушения',
            'testMobSkillDamageEffect' => 'Эффект урона',
            'testMobSkillDotEffect' => 'Эффект урона со временем',
            'testMobSkillCooldownSystem' => 'Система кулдаунов',
            'testMobSkillHealthConditions' => 'Условия здоровья',
            'testBattleLogIntegration' => 'Интеграция с боевыми логами'
        ];

        $passed = 0;
        $total = count($tests);

        foreach ($tests as $method => $description) {
            $this->info("🧪 Тестирование: {$description}");

            try {
                $this->$method();
                $this->info("   ✅ Пройден");
                $passed++;
            } catch (\Exception $e) {
                $this->error("   ❌ Провален: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("📊 Результаты тестирования:");
        $this->info("   Пройдено: {$passed}/{$total}");

        if ($passed === $total) {
            $this->info("   🎉 Все тесты успешно пройдены!");
        } else {
            $this->warn("   ⚠️  Некоторые тесты провалены");
        }
    }

    protected function testCreateMobSkillTemplate()
    {
        $templateData = [
            'name' => 'Тестовый Удар',
            'description' => 'Тестовое описание скилла',
            'icon' => 'assets/skills/test.png',
            'effect_type' => 'damage',
            'effect_data' => [
                'damage' => 100,
                'message' => 'Получен урон от тестового удара!'
            ],
            'chance' => 30,
            'cooldown' => 15,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 5
        ];

        $template = MobSkillTemplate::create($templateData);

        if (!$template || $template->name !== 'Тестовый Удар') {
            throw new \Exception('Не удалось создать шаблон скилла');
        }
    }

    protected function testAssignSkillToMob()
    {
        $template = MobSkillTemplate::create([
            'name' => 'Тестовое Оглушение',
            'description' => 'Оглушает цель на несколько секунд',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 5,
                'disable_skills' => true,
                'message' => 'Вы оглушены!'
            ],
            'chance' => 25,
            'cooldown' => 20,
            'duration' => 5,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 7
        ]);

        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id,
            'chance' => 35
        ]);

        if (!$mobSkill || $mobSkill->mob_id !== $this->testMob->id) {
            throw new \Exception('Не удалось привязать скилл к мобу');
        }
    }

    protected function testMobSkillDamageEffect()
    {
        $initialHp = $this->testUser->profile->hp;

        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Урон',
            'effect_type' => 'damage',
            'effect_data' => [
                'damage' => 150,
                'message' => 'Получен урон от тестового скилла!'
            ],
            'chance' => 100,
            'cooldown' => 10,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 6
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        if (empty($results) || !$results[0]['success']) {
            throw new \Exception('Скилл урона не сработал');
        }

        $this->testUser->profile->refresh();
        if ($this->testUser->profile->hp >= $initialHp) {
            throw new \Exception('HP не уменьшилось после урона');
        }
    }

    protected function testMobSkillStunEffect()
    {
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Стан',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 10,
                'disable_skills' => true,
                'message' => 'Вы оглушены!'
            ],
            'chance' => 100,
            'cooldown' => 30,
            'duration' => 10,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 8
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        if (empty($results) || !$results[0]['success'] || $results[0]['effect_type'] !== 'stun') {
            throw new \Exception('Эффект стана не сработал');
        }

        $activeEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('caster_type', 'mob')
            ->where('caster_id', $this->testMob->id)
            ->first();

        if (!$activeEffect || $activeEffect->effect_data['type'] !== 'stun') {
            throw new \Exception('Активный эффект стана не создался');
        }
    }

    protected function testMobSkillDotEffect()
    {
        $template = MobSkillTemplate::create([
            'name' => 'Тестовое Кровотечение',
            'effect_type' => 'dot',
            'effect_data' => [
                'damage_per_tick' => 20,
                'tick_interval' => 2,
                'total_duration' => 10,
                'message' => 'Вы истекаете кровью!'
            ],
            'chance' => 100,
            'cooldown' => 25,
            'duration' => 10,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 4
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        if (empty($results) || !$results[0]['success'] || $results[0]['effect_type'] !== 'dot') {
            throw new \Exception('Эффект DOT не сработал');
        }
    }

    protected function testMobSkillCooldownSystem()
    {
        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Кулдаун',
            'effect_type' => 'damage',
            'effect_data' => ['damage' => 50],
            'chance' => 100,
            'cooldown' => 5,
            'duration' => 0,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 5
        ]);

        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // Первое использование
        $results1 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        if (empty($results1) || !$results1[0]['success']) {
            throw new \Exception('Первое использование скилла не сработало');
        }

        // Проверяем кулдаун
        $mobSkill->refresh();
        if (!$mobSkill->cooldown_ends_at) {
            throw new \Exception('Кулдаун не установился');
        }

        // Второе использование сразу не должно сработать
        $results2 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        if (!empty($results2)) {
            throw new \Exception('Скилл сработал повторно несмотря на кулдаун');
        }
    }

    protected function testMobSkillHealthConditions()
    {
        $template = MobSkillTemplate::create([
            'name' => 'Тестовая Ярость',
            'effect_type' => 'buff',
            'effect_data' => [
                'damage_multiplier' => 1.5,
                'duration' => 15
            ],
            'chance' => 100,
            'cooldown' => 30,
            'duration' => 15,
            'target_type' => 'self',
            'min_health_percent' => 0,
            'max_health_percent' => 30,
            'is_active' => true,
            'priority' => 9
        ]);

        MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $template->id
        ]);

        // При полном HP скилл не должен сработать
        $results1 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        if (!empty($results1)) {
            throw new \Exception('Скилл сработал при полном HP');
        }

        // Уменьшаем HP моба
        $this->testMob->hp = (int) ($this->testMob->max_hp * 0.25);
        $this->testMob->save();

        // Теперь скилл должен сработать
        $results2 = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);
        if (empty($results2) || !$results2[0]['success']) {
            throw new \Exception('Скилл не сработал при низком HP');
        }
    }

    protected function testBattleLogIntegration()
    {
        Redis::flushall();

        $template = MobSkillTemplate::create([
            'name' => 'Тестовый Лог',
            'effect_type' => 'stun',
            'effect_data' => [
                'duration' => 3,
                'message' => 'Тестовое оглушение!'
            ],
            'chance' => 100,
            'cooldown' => 10,
            'duration' => 3,
            'target_type' => 'player',
            'min_health_percent' => 0,
            'max_health_percent' => 100,
            'is_active' => true,
            'priority' => 7
        ]);

        $this->battleLogService->logMobSkillUsage(
            $this->testMob->id,
            $this->testMob->name,
            $this->testUser->id,
            $this->testUser->name,
            $template->name,
            $template->effect_type,
            $template->effect_data,
            'test_location'
        );

        $battleLogKey = "location:test_location:{$this->testUser->id}";
        $logs = $this->battleLogService->getLogs($battleLogKey);

        if (empty($logs) || !str_contains($logs[0]['message'], 'оглушил')) {
            throw new \Exception('Лог скилла не добавился');
        }
    }
}
