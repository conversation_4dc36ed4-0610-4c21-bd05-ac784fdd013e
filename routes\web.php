<?php
// Маршрут для страницы технических работ (должен быть доступен всегда)
Route::get('/maintenance', [App\Http\Controllers\MaintenanceController::class, 'show'])
    ->name('maintenance.page');

// Самый первый маршрут для теста
Route::get('/test-route-works', function () {
    return 'Test Route OK!';
});

// СПЕЦИАЛЬНЫЙ МАРШРУТ ДЛЯ ДОСТУПА РАЗРАБОТЧИКОВ
// Этот маршрут должен быть доступен всегда, даже во время технических работ
// КРИТИЧЕСКИ ВАЖНО: Исключаем MaintenanceModeMiddleware для этого маршрута
Route::get(
    '/secret-dev-access/dev_bypass_2024_secure_token_x9k2m8n5p7q1w3e6r4t8y2u9i1o5p3a7s9d2f4g6h8j0k2l4z6x8c0v2b4n6m8',
    [App\Http\Controllers\DevAccessController::class, 'grantAccess']
)
    ->name('dev.access.grant')
    ->withoutMiddleware([\App\Http\Middleware\MaintenanceModeMiddleware::class]);

// Тестовая страница для проверки наград от мобов
Route::get('/test-mob-rewards', function () {
    return view('test-mob-rewards');
})->name('test.mob.rewards');

// Тестовая страница для проверки компонентов мобов с эффектами
Route::get('/test-mob-effects', function () {
    // Получаем моба с активными эффектами
    $mob = \App\Models\DungeonInstanceMob::with(['activeEffects.skill'])->first();

    if (!$mob) {
        return response('Мобы не найдены в подземельях', 404);
    }

    $effectsCount = $mob->activeEffects()->count();

    $html = '<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест компонента мобов</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-4">
    <h1 class="text-2xl font-bold mb-4">Тест компонента активных эффектов мобов</h1>

    <div class="bg-gray-800 p-4 rounded">
        <h2 class="text-lg mb-2">Статус компонента:</h2>
        <p>✅ Компонент mob-active-effects.blade.php существует</p>
        <p>✅ Компонент подключен в mob-switcher.blade.php</p>
        <p>✅ Кеш представлений очищен</p>
        <p>✅ Сервер запущен</p>
        <p>✅ Morph map для DungeonInstanceMob добавлен</p>
        <p>✅ Тестовые эффекты созданы</p>

        <div class="mt-4 p-3 bg-green-800 rounded">
            <h3 class="font-bold">Данные моба:</h3>
            <p>Моб: ' . $mob->name . ' (ID: ' . $mob->id . ')</p>
            <p>Активных эффектов: ' . $effectsCount . '</p>
        </div>

        <div class="mt-4 p-3 bg-blue-800 rounded">
            <h3 class="font-bold">Проверьте страницу подземелья:</h3>
            <p><a href="/dungeons/6/battle" class="text-blue-300 underline">Перейти к подземелью</a></p>
            <p>Теперь должны отображаться активные эффекты между иконкой и именем моба</p>
        </div>
    </div>
</body>
</html>';

    return response($html);
})->name('test.mob.effects');

// Отладочная страница для профиля
Route::get('/debug-profile', function () {
    return view('debug-profile');
})->name('debug.profile');

// Быстрая авторизация для тестирования
Route::get('/quick-login/{userId?}', function ($userId = 1) {
    $user = \App\Models\User::find($userId);
    if (!$user) {
        return "Пользователь с ID {$userId} не найден";
    }

    \Illuminate\Support\Facades\Auth::login($user);

    return "Авторизован как: {$user->name} (ID: {$user->id})<br>" .
        "<a href='/user'>Перейти к профилю</a><br>" .
        "<a href='/debug-profile'>Отладочная страница</a><br>" .
        "<a href='/test-dungeon-mobs'>Тест мобов подземелья</a>";
})->name('quick.login');

// Тестовая страница для мобов подземелья
Route::get('/test-dungeon-mobs', function () {
    $user = \Illuminate\Support\Facades\Auth::user();
    if (!$user) {
        return redirect('/quick-login/1');
    }

    $dungeon = \App\Models\Dungeon::find(6);
    $locationName = 'Подземелье: ' . $dungeon->name;
    $mobsInDungeon = \App\Models\Mob::where('location', $locationName)
        ->where('hp', '>', 0)
        ->where(function ($query) {
            $query->whereNull('death_time')
                ->orWhereRaw('death_time <= NOW() - (respawn_time * INTERVAL \'1 minute\')');
        })
        ->orderBy('id')
        ->get();

    return view('test-dungeon-mobs', compact('user', 'dungeon', 'mobsInDungeon'));
})->name('test.dungeon.mobs');

// Проверка текущего пользователя
Route::get('/who-am-i', function () {
    if (\Illuminate\Support\Facades\Auth::check()) {
        $user = \Illuminate\Support\Facades\Auth::user();
        return "Авторизован как: {$user->name} (ID: {$user->id}, Email: {$user->email})<br>" .
            "<a href='/user'>Перейти к профилю</a><br>" .
            "<a href='/debug-profile'>Отладочная страница</a><br>" .
            "<a href='/quick-login/1'>Авторизоваться как пользователь 1</a>";
    } else {
        return "Не авторизован<br>" .
            "<a href='/quick-login/1'>Авторизоваться как пользователь 1</a>";
    }
})->name('who.am.i');

// Тестовый маршрут для проверки фильтрации ботов
Route::get('/test-bot-filtering/{userId}/{location}', function ($userId, $location) {
    $user = \App\Models\User::with('profile')->find($userId);
    if (!$user || !$user->profile) {
        return response()->json(['error' => 'Пользователь не найден']);
    }

    $userRace = $user->profile->race;

    // Получаем всех ботов в локации
    $allBots = \App\Models\Bot::where('location', $location)
        ->where('is_active', true)
        ->where('hp', '>', 0)
        ->get();

    // Тестируем FactionCountService
    $factionCountService = app(\App\Services\battle\FactionCountService::class);
    $factionCounts = $factionCountService->getLocationFactionCounts($location, $userRace);

    return response()->json([
        'user' => [
            'id' => $user->id,
            'name' => $user->name,
            'race' => $userRace
        ],
        'location' => $location,
        'all_bots' => $allBots->map(function ($bot) use ($userRace) {
            return [
                'id' => $bot->id,
                'name' => $bot->name,
                'race' => $bot->race,
                'class' => $bot->class,
                'hp' => $bot->hp,
                'can_attack' => $bot->race !== $userRace,
                'reason' => $bot->race !== $userRace ? 'противоположная раса' : 'та же раса (союзник)'
            ];
        }),
        'faction_counts' => $factionCounts
    ]);
})->name('test.bot.filtering');

// Простая HTML страница для тестирования
Route::get('/test-bot-filtering-page', function () {
    return '<!DOCTYPE html>
<html>
<head>
    <title>Тест фильтрации ботов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .bot { margin: 5px 0; padding: 5px; }
        .can-attack { background-color: #ffcccc; }
        .cannot-attack { background-color: #ccffcc; }
    </style>
</head>
<body>
    <h1>Тест фильтрации ботов в аванпостах</h1>
    <p>Проверяем, что боты отображаются в счетчике, но только враги доступны для атаки</p>

    <div>
        <label>User ID: <input type="number" id="userId" value="1"></label>
        <label>Location: <input type="text" id="location" value="avanpost_test"></label>
        <button onclick="testFiltering()">Тестировать</button>
    </div>

    <div id="results"></div>

    <script>
        function testFiltering() {
            const userId = document.getElementById("userId").value;
            const location = document.getElementById("location").value;

            fetch(`/test-bot-filtering/${userId}/${location}`)
                .then(response => response.json())
                .then(data => {
                    let html = `<div class="result">
                        <h3>Пользователь: ${data.user.name} (${data.user.race})</h3>
                        <h3>Локация: ${data.location}</h3>

                        <h4>Боты в локации:</h4>`;

                    if (data.all_bots.length === 0) {
                        html += `<p>Ботов в локации не найдено</p>`;
                    } else {
                        data.all_bots.forEach(bot => {
                            const cssClass = bot.can_attack ? "can-attack" : "cannot-attack";
                            const status = bot.can_attack ? "✅ МОЖНО АТАКОВАТЬ" : "❌ НЕЛЬЗЯ АТАКОВАТЬ";
                            html += `<div class="bot ${cssClass}">
                                ${status} | ${bot.name} (${bot.race} ${bot.class}) HP: ${bot.hp} | ${bot.reason}
                            </div>`;
                        });
                    }

                    html += `<h4>Счетчик фракций:</h4>
                        <p>Solarius: Воины: ${data.faction_counts.total_counts.solarius.warriors}, Маги: ${data.faction_counts.total_counts.solarius.mages}, Жрецы: ${data.faction_counts.total_counts.solarius.knights}</p>
                        <p>Lunarius: Воины: ${data.faction_counts.total_counts.lunarius.warriors}, Маги: ${data.faction_counts.total_counts.lunarius.mages}, Жрецы: ${data.faction_counts.total_counts.lunarius.knights}</p>
                    </div>`;

                    document.getElementById("results").innerHTML = html;
                })
                .catch(error => {
                    document.getElementById("results").innerHTML = `<div class="result">Ошибка: ${error}</div>`;
                });
        }
    </script>
</body>
</html>';
})->name('test.bot.filtering.page');
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\RegisterController;
use Illuminate\Support\Facades\Auth;
use App\Http\Middleware\LogoutIfAuthenticated;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\DB;
use App\Http\Middleware\UserActivity;
use App\Http\Middleware\ResetUserTargets;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\BattleController;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\CheckMinimumHP;
use App\Http\Controllers\Admin\ItemController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\EquipmentController;
use App\Models\Location;
use App\Http\Controllers\Outposts\OutpostsController;
use App\Http\Controllers\Mines\MinesController;
use App\Http\Controllers\Mines\CustomMineController;
use App\Http\Controllers\Masters\AlchemistController;
use App\Http\Controllers\Admin\LocationController;
// Импортируем контроллер обелисков
use App\Http\Controllers\Admin\ObeliskController;

// Правильный путь
use App\Http\Controllers\Outposts\ElvenHavenController;
use App\Http\Controllers\Outposts\DawnFortController;
use App\Http\Controllers\Masters\MastersController;
use App\Http\Controllers\Masters\BuyerController;
use App\Http\Controllers\Masters\BlacksmithController;

use App\Http\Controllers\Farming\FarmingController;
use App\Http\Controllers\Farming\BedsController;
use App\Http\Controllers\Farming\SeedsController;
use App\Http\Controllers\Farming\FertilizersController;
use App\Http\Controllers\Farming\WorkersController;
use App\Http\Controllers\MobResourceDropController;
use App\Http\Controllers\MobController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\Forum\NewsController;
use App\Http\Controllers\Forum\NewsCommentController;
use App\Http\Controllers\Forum\NewsReactionController;
use App\Models\PotionRecipe;
use App\Http\Controllers\Admin\PotionController;
use App\Http\Controllers\Admin\BotController;
use App\Http\Controllers\MessageController;
// Импортируем контроллер для управления магазином в админке
use App\Http\Controllers\Admin\ShopAdminController;
use App\Http\Controllers\Outposts\SandyStrongholdController;
use App\Http\Controllers\GuildController;
use App\Http\Controllers\GuildBuildingController;

// ГЛАВНАЯ СТРАНИЦА: Отображение страницы технических работ
// Все пользователи видят страницу завершения бета-теста
Route::get('/', [App\Http\Controllers\MaintenanceController::class, 'show'])
    ->name('home.maintenance');

// Дополнительные маршруты для доступа разработчиков
// КРИТИЧЕСКИ ВАЖНО: Исключаем MaintenanceModeMiddleware ТОЛЬКО для критических маршрутов управления
Route::prefix('dev-access')->name('dev.access.')
    ->withoutMiddleware([\App\Http\Middleware\MaintenanceModeMiddleware::class])
    ->group(function () {
        Route::get('/choice', [App\Http\Controllers\DevAccessController::class, 'showChoice'])->name('choice');
        Route::get('/status', [App\Http\Controllers\DevAccessController::class, 'checkStatus'])->name('status');
        Route::get('/revoke', [App\Http\Controllers\DevAccessController::class, 'revokeAccess'])->name('revoke');
    });

// Тестовая страница разработчиков - ОТДЕЛЬНО, с применением MaintenanceModeMiddleware
// БЕЗОПАСНОСТЬ: Требует токен разработчика через MaintenanceModeMiddleware
Route::get('/dev-access/test', function () {
    return view('test-dev-access');
})->name('dev.access.test');

// ЗАГЛУШКИ для старой системы access (для совместимости)
Route::prefix('access')->name('access.')->group(function () {
    Route::get('/welcome', function () {
        return redirect()->route('maintenance.page');
    })->name('welcome');

    Route::post('/verify', function () {
        return redirect()->route('maintenance.page');
    })->name('verify');

    Route::get('/status', function () {
        return response()->json(['has_access' => false, 'redirect' => route('maintenance.page')]);
    })->name('status');

    Route::get('/logout', function () {
        return redirect()->route('maintenance.page');
    })->name('logout');
});

// МАРШРУТЫ АУТЕНТИФИКАЦИИ - доступны только разработчикам
Route::prefix('auth')->name('auth.')->group(function () {
    // Защищенные маршруты регистрации
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])
        ->middleware(LogoutIfAuthenticated::class)
        ->name('register');
    Route::post('/register', [RegisterController::class, 'register'])->name('register.submit');

    // Защищенные маршруты авторизации
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login'])->name('login.submit');

    // Маршрут выхода
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
});

// УДАЛЕНО: Административные маршруты для управления ключами доступа
// Система ключей доступа заменена на страницу технических работ

// Старый маршрут для совместимости (перенаправляет на систему доступа)
Route::get('/index-old', function () {
    if (auth()->check()) {
        return redirect('/home');  // Используем прямой путь вместо route()
    }
    return view('index');
})->middleware('web')->name('index.old');

// УДАЛЕНО: Маршруты для системы приветствия и пролога
// Система ключей доступа заменена на страницу технических работ

// Отдельная группа для маршрутов завершения регистрации пролога (БЕЗ check.prologue middleware)
Route::prefix('prologue')->name('prologue.')->middleware(['auth', 'user.activity', 'handle.ActiveEffects'])->group(function () {
    Route::get('/registration/name', [App\Http\Controllers\PrologueRegistrationController::class, 'showNameForm'])->name('registration.name');
    Route::post('/registration/save-name', [App\Http\Controllers\PrologueRegistrationController::class, 'saveName'])->name('registration.save-name');
    Route::get('/registration/password', [App\Http\Controllers\PrologueRegistrationController::class, 'showPasswordForm'])->name('registration.password');
    Route::post('/registration/complete', [App\Http\Controllers\PrologueRegistrationController::class, 'completeRegistration'])->name('registration.complete');
});






// Защищённые маршруты
Route::middleware(['auth', 'user.activity', 'handle.ActiveEffects', 'check.prologue', 'check.forced.redirect', 'check.dungeon.access'])->group(function () {

    Route::get('/test-view', [App\Http\Controllers\TestSampleController::class, 'index']);

    // Главная страница
    Route::get('/home', [UserController::class, 'showInterfaceResurces'])
        ->middleware('reset.targets')
        ->name('home');

    // Альтернативный маршрут для главной страницы (для решения проблемы с route('home'))
    Route::get('/dashboard', [UserController::class, 'showInterfaceResurces'])
        ->middleware('reset.targets')
        ->name('dashboard');

    // Маршруты для городской площади
    Route::get('/square', [App\Http\Controllers\SquareController::class, 'index'])->name('square.index');

    // Маршруты для банка
    Route::prefix('bank')->name('bank.')->group(function () {
        Route::get('/', [App\Http\Controllers\BankController::class, 'index'])->name('index');
        Route::post('/move-to-bank', [App\Http\Controllers\BankController::class, 'moveToBank'])->name('move-to-bank');
        Route::post('/move-to-inventory', [App\Http\Controllers\BankController::class, 'moveToInventory'])->name('move-to-inventory');
        Route::post('/upgrade-storage', [App\Http\Controllers\BankController::class, 'upgradeStorage'])->name('upgrade-storage');

        // Маршруты для транзакций
        Route::prefix('transactions')->name('transactions.')->group(function () {
            Route::get('/', [App\Http\Controllers\BankTransactionController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\BankTransactionController::class, 'create'])->name('create');
            Route::post('/store', [App\Http\Controllers\BankTransactionController::class, 'store'])->name('store');
            Route::get('/{id}', [App\Http\Controllers\BankTransactionController::class, 'show'])->name('show');
        });
    });

    // Маршруты для рынка
    Route::prefix('market')->name('market.')->group(function () {
        Route::get('/', [App\Http\Controllers\MarketController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\MarketController::class, 'create'])->name('create');
        Route::post('/store', [App\Http\Controllers\MarketController::class, 'store'])->name('store');
        Route::get('/slots', [App\Http\Controllers\MarketController::class, 'slots'])->name('slots');
        Route::post('/slots/buy', [App\Http\Controllers\MarketController::class, 'buySlots'])->name('buy-slots');
        Route::get('/my-listings', [App\Http\Controllers\MarketController::class, 'myListings'])->name('my-listings');
        Route::post('/toggle-filter', [App\Http\Controllers\MarketController::class, 'toggleFilter'])->name('toggle-filter');
        Route::get('/{id}', [App\Http\Controllers\MarketController::class, 'show'])->name('show')->where('id', '[0-9]+');
        Route::post('/{id}/buy', [App\Http\Controllers\MarketController::class, 'buy'])->name('buy')->where('id', '[0-9]+');
        Route::post('/{id}/cancel', [App\Http\Controllers\MarketController::class, 'cancel'])->name('cancel')->where('id', '[0-9]+');
    });

    // Группировка маршрутов для магазина
    Route::prefix('shop')->name('shop.')->middleware('auth', 'activity', 'verified')->group(function () {
        Route::get('/', [ShopController::class, 'index'])->name('index'); // /shop
        Route::get('/recipes', [ShopController::class, 'recipes'])->name('recipes'); // /shop/recipes
        Route::get('/resources', [ShopController::class, 'resources'])->name('resources'); // /shop/resources

        Route::post('/buy/{shopItem}', [ShopController::class, 'buy'])->name('buy'); // /shop/buy/{shopItem}
        Route::post('/buy-recipe/{recipe}', [ShopController::class, 'buyRecipe'])->name('buy.recipe'); // /shop/buy-recipe/{recipe}
        Route::post('/generate-token', [ShopController::class, 'generatePurchaseToken'])->name('generateToken'); // /shop/generate-token

        // Исправленный маршрут для AJAX-модалки деталей предмета
        // Этот маршрут вызывается из JS (showItemDetailsModal) и возвращает JSON с HTML для модального окна
        Route::get('/item-details/{shopItem}', [\App\Http\Controllers\ShopController::class, 'getItemDetails'])->name('item.details.ajax'); // /shop/item-details/{shopItem}

        // (Опционально) Если нужна отдельная страница с деталями предмета — используйте этот маршрут
        // Route::get('/item/{shopItem}', [\App\Http\Controllers\ShopController::class, 'showItemDetails'])->name('item.details.page'); // /shop/item/{shopItem}

        // *** НОВЫЙ МАРШРУТ ДЛЯ ПОЛУЧЕНИЯ ДЕТАЛЕЙ РЕЦЕПТА (AJAX) ***
        // *** NEW ROUTE TO GET RECIPE DETAILS (AJAX) ***
        Route::get('/recipe-details/{recipe}', function (PotionRecipe $recipe) {
            // Проверяем, существует ли рецепт // Check if recipe exists
            if (!$recipe) {
                return response()->json(['error' => 'Рецепт не найден'], 404);
            }
            // Загружаем необходимые связи: ингредиенты и их ресурсы
            $recipe->load('ingredients.resource');

            // Вручную добавляем полный путь к иконкам ингредиентов
            // И аксессор для самого рецепта будет вызван при рендеринге view
            foreach ($recipe->ingredients as $ingredient) {
                // Используем аксессор icon_path из модели AlchemyIngredient
                $ingredient->icon_path_for_modal = $ingredient->icon_path;
            }

            // Рендерим частичный шаблон с деталями рецепта
            $html = view('shop.partials.recipe_details', compact('recipe'))->render();
            // Возвращаем JSON с HTML-кодом
            return response()->json(['html' => $html]);
        })->name('recipe.details.ajax');
    });

    // Отдельные маршруты для каждого мастера
    Route::prefix('masters')->name('masters.')->group(function () {
        Route::get('/', [MastersController::class, 'index'])->name('index');

        // Скупщик
        Route::get('/buyer', [BuyerController::class, 'index'])->name('buyer');
        Route::post('/buyer/sell', [BuyerController::class, 'sell'])->name('buyer.sell');


        //Кузнец
        Route::get('/blacksmith', [BlacksmithController::class, 'index'])->name('blacksmith');

        // Мастера в разработке
        Route::get('/inlay', [MastersController::class, 'inlay'])->name('inlay');
        Route::get('/magister', [MastersController::class, 'magister'])->name('magister');
        Route::get('/guardsman', [MastersController::class, 'guardsman'])->name('guardsman');
        Route::get('/board', [MastersController::class, 'board'])->name('board');
        // Положить ресурс в слот
        Route::post('/blacksmith/place', [BlacksmithController::class, 'placeResource'])
            ->name('blacksmith.place');
        // Убрать ресурс из слота
        Route::post('/blacksmith/remove', [BlacksmithController::class, 'removeResource'])
            ->name('blacksmith.remove');
        // Использование предмета в кузнице

        // Поместить предмет в центральный слот
        Route::post('/blacksmith/place-item', [BlacksmithController::class, 'placeItemInSlot'])
            ->name('blacksmith.place_item');

        // Удалить предмет из центрального слота
        Route::post('/blacksmith/remove-item', [BlacksmithController::class, 'removeItemFromSlot'])
            ->name('blacksmith.remove_item');

        Route::post('/blacksmith/use-item', [BlacksmithController::class, 'useItem'])
            ->name('blacksmith.use_item');

        //Приминить улучшение
        Route::post('/blacksmith/ascend', [BlacksmithController::class, 'applyAscensionSphere'])
            ->name('blacksmith.ascend');
        Route::post('/blacksmith/start-upgrade', [BlacksmithController::class, 'startUpgrade'])
            ->name('blacksmith.startUpgrade');
        Route::get('/blacksmith/check-upgrade-status', [BlacksmithController::class, 'checkUpgradeStatus'])
            // ->middleware('throttle.blacksmith') // Временно отключено для отладки
            ->name('blacksmith.checkUpgradeStatus');

        //Ремонт предмета
        Route::post('/blacksmith/start-repair', [BlacksmithController::class, 'startRepair'])
            ->name('blacksmith.startRepair');
        Route::get('/blacksmith/check-repair-status', [BlacksmithController::class, 'checkRepairStatus'])
            // ->middleware('throttle.blacksmith') // Временно отключено для отладки
            ->name('blacksmith.checkRepairStatus');

        // Запуск расплавления (старая система)
        Route::post('/blacksmith/start-melt', [BlacksmithController::class, 'startMelt'])
            ->name('blacksmith.startMelt');
        // Проверка статуса расплавления (старая система)
        Route::get('/blacksmith/check-melt-status', [BlacksmithController::class, 'checkMeltStatus'])
            // ->middleware('throttle.blacksmith') // Временно отключено для отладки
            ->name('blacksmith.checkMeltStatus');

        // Новая система расплавления с PostgreSQL
        Route::post('/blacksmith/start-melt-new', [BlacksmithController::class, 'startMeltNew'])
            ->name('blacksmith.startMeltNew');
        Route::get('/blacksmith/check-melt-status-new', [BlacksmithController::class, 'checkMeltStatusNew'])
            // ->middleware('throttle.blacksmith') // Временно отключено для отладки
            ->name('blacksmith.checkMeltStatusNew');
        Route::post('/blacksmith/complete-melt-new', [BlacksmithController::class, 'completeMeltNew'])
            ->name('blacksmith.completeMeltNew');

        // получения уведомлений из Redis
        Route::get('/blacksmith/get-latest-notification', [BlacksmithController::class, 'getLatestNotification'])
            ->name('blacksmith.getLatestNotification');
        // Чистим уведомление из Redis
        Route::post('/blacksmith/clear-notification', [BlacksmithController::class, 'clearNotification'])
            ->name('blacksmith.clearNotification');

        Route::post('/masters/blacksmith/clear-logs', [BlacksmithController::class, 'clearLogs'])
            ->name('blacksmith.clear-logs');


        Route::get('/alchemist', [AlchemistController::class, 'index'])->name('alchemist');
        Route::get('/alchemist/enhancement', [AlchemistController::class, 'enhancement'])->name('alchemist.enhancement');

        // Маршрут для обычного POST-запроса на сбор зелья (без JSON-ответа)
        Route::post('/alchemist/collect-potion-redirect', [AlchemistController::class, 'collectPotionRedirect'])->name('alchemist.collectPotionRedirect');

        // Группа маршрутов для АПИ алхимика с JSON-ответами
        Route::middleware(['json.response'])->group(function () {
            // Новые маршруты для алхимика
            Route::post('/alchemist/start-brewing', [AlchemistController::class, 'startBrewing'])->name('alchemist.startBrewing');
            Route::get('/alchemist/check-brewing-status', [AlchemistController::class, 'checkBrewingStatus'])->name('alchemist.checkBrewingStatus')->middleware('throttle:60,1');
            Route::post('/alchemist/collect-potion', [AlchemistController::class, 'collectPotion'])->name('alchemist.collectPotion');
            Route::post('/alchemist/enhance-potion', [AlchemistController::class, 'enhancePotion'])->name('alchemist.enhancePotion');
            Route::post('/alchemist/clear-logs', [AlchemistController::class, 'clearLogs'])->name('alchemist.clearLogs');
            Route::post('/alchemist/cancel-brewing', [AlchemistController::class, 'cancelBrewing'])->name('alchemist.cancelBrewing');
            Route::post('/alchemist/check-ingredients', [AlchemistController::class, 'checkIngredients'])->name('alchemist.checkIngredients');
        });
    });

    // Группировка маршрутов для пользователя
    Route::prefix('user')->name('user.')->group(function () {
        Route::get('/', [UserController::class, 'showCurrentUser'])->name('profile'); // /user
        Route::get('/{id}', [UserController::class, 'showUser'])
            ->where('id', '[0-9]+')
            ->name('profile.other'); // /user/{id}

        Route::get('/equipment/item/{gameItem}', [UserController::class, 'showEquippedItemDetails'])
            ->name('equipped-item.details');

        // Снаряжение другого пользователя
        Route::get('/{userId}/equipment', [UserController::class, 'showOtherUserEquipment'])
            ->where('userId', '[0-9]+')
            ->name('equipment.other'); // /user/{userId}/equipment

        // Детали экипированного предмета другого пользователя
        Route::get('/{userId}/equipment/item/{gameItemId}', [UserController::class, 'showOtherUserEquippedItemDetails'])
            ->where(['userId' => '[0-9]+', 'gameItemId' => '[0-9]+'])
            ->name('equipped-item.details.other'); // /user/{userId}/equipment/item/{gameItemId}

        // Старый маршрут, возможно, уже не нужен или переименовать?
// Route::get('/equipment/{id}', [UserController::class, 'showEquipmentDetails'])->name('equipment.details'); // /user/equipment/{id}
        Route::get('/equipment', [UserController::class, 'showEquipment'])->name('equipment'); // /user/equipment
        Route::get('/equipment/{id}', [UserController::class, 'showEquipmentDetails'])->name('equipment.details'); // /user/equipment/{id}
        Route::post('/unequip', [EquipmentController::class, 'unequip'])->name('unequip');
    });

    // Инвентарь и экипировка (с оптимизацией для высоких нагрузок)
    Route::prefix('inventory')->name('inventory.')->middleware('inventory.optimization')->group(function () {
        Route::get('/', [InventoryController::class, 'index'])->name('index'); // /inventory
        Route::post('/equip', [EquipmentController::class, 'equip'])->name('inventory.equip'); // /inventory/equip
        // Маршрут для выбрасывания ресурса
        Route::delete('/drop-resource/{userResource}', [InventoryController::class, 'dropResource'])->name('drop-resource');
        // Маршрут для выбрасывания рецепта
        Route::post('/drop-recipe/{userRecipe}', [InventoryController::class, 'dropRecipe'])->name('drop-recipe');
        // Маршрут для выбрасывания алхимического ингредиента
        Route::post('/drop-ingredient/{userIngredient}', [InventoryController::class, 'dropIngredient'])->name('drop-ingredient');
        // Маршрут для выбрасывания урожая
        Route::post('/drop-harvest/{userHarvest}', [InventoryController::class, 'dropHarvest'])->name('drop-harvest');
        // Маршрут для выбрасывания предмета
        Route::delete('/drop-item/{gameItem}', [InventoryController::class, 'dropItem'])->name('drop-item');
        // Маршрут для выбрасывания зелья (старая версия)
        Route::delete('/drop-potion/{potion}', [InventoryController::class, 'dropPotion'])->name('drop-potion');
        // Маршрут для выбрасывания зелья (новая версия)
        Route::delete('/drop-user-potion/{userPotion}', [InventoryController::class, 'dropUserPotion'])->name('drop-user-potion');
        // Маршрут для использования зелья
        Route::post('/use-potion/{potion}', [InventoryController::class, 'usePotion'])->name('use-potion');
        // Маршрут для получения информации о предмете
        Route::get('/item-info/{gameItem}', [InventoryController::class, 'getItemInfo'])->name('item-info');
        // Маршрут для пересчета инвентаря
        Route::get('/recalculate', [InventoryController::class, 'recalculateInventory'])->name('recalculate');

        // Маршруты для пояса зелий
        Route::prefix('potion-belt')->name('potion-belt.')->group(function () {
            Route::post('/add', [App\Http\Controllers\PotionBeltController::class, 'addToBelt'])->name('add');
            Route::post('/remove', [App\Http\Controllers\PotionBeltController::class, 'removeFromBelt'])->name('remove');
            Route::post('/use', [App\Http\Controllers\PotionBeltController::class, 'usePotionFromBelt'])->name('use');
        });
    });

    // Маршруты для управления эффектами предметов
    Route::prefix('item-effects')->name('item-effects.')->group(function () {
        Route::post('/add', [App\Http\Controllers\ItemEffectController::class, 'addEffect'])->name('add');
        Route::post('/remove', [App\Http\Controllers\ItemEffectController::class, 'removeEffect'])->name('remove');
        Route::post('/clear', [App\Http\Controllers\ItemEffectController::class, 'clearEffects'])->name('clear');
        Route::post('/add-preset', [App\Http\Controllers\ItemEffectController::class, 'addPresetEffect'])->name('add-preset');
    });

    // // Battle
    // Route::prefix('battle')->name('battle.')->group(function () {
    //     Route::get('/', [OutpostsController::class, 'index'])->name('index'); // /
    //     Route::get('/outposts', [BattleController::class, 'index'])->name('outposts'); // /
    //     Route::get('/outposts/Elven_Haven', [BattleController::class, 'ElvenHaven'])->name('ElvenHaven'); // /
    //     Route::post('/select-mob', [BattleController::class, 'selectMob'])->name('select.mob');
    //     Route::post('/attack', [BattleController::class, 'attack'])->name('attack');
    // });
    // Battle Routes
    // Battle Routes
    // Battle Routes


    // Battle
    // Главная группа "Битва"
    Route::prefix('battle')->name('battle.')->group(function () {
        // Главная страница битвы
        Route::get('/', [BattleController::class, 'index'])->name('index')->middleware('reset.targets'); // battle.index

        // Страница поражения
        Route::get('/defeat', [BattleController::class, 'defeat'])->name('defeat'); // battle.defeat

        // Маршрут для возрождения после поражения
        Route::post('/respawn/{location?}', [BattleController::class, 'respawn'])->name('respawn'); // battle.respawn


        // Группа для аванпостов
        Route::prefix('outposts')->name('outposts.')->group(function () {
            // Список аванпостов
            Route::get('/', [OutpostsController::class, 'index'])->middleware('resetTarget.OnLocationChange')->name('index'); // battle.outposts.index

            // Маршруты для пользовательских аванпостов
            // Основной маршрут для отображения локации
            Route::get('/{id}', function ($id) {
                // Сначала проверяем, есть ли ID в таблице outpost_locations
                $outpostLocation = \App\Models\OutpostLocation::find($id);
                if ($outpostLocation && $outpostLocation->location_id) {
                    // Если это пользовательский аванпост, используем связанный location_id
                    return app(\App\Http\Controllers\Outposts\CustomOutpostController::class)->index($outpostLocation->location_id);
                }

                // Проверяем, является ли это ознакомительным аванпостом в таблице locations
                $location = \App\Models\Location::find($id);
                if ($location && $location->name === 'Ознакомительный Аванпост') {
                    return app(\App\Http\Controllers\Outposts\PrologueOutpostController::class)->index($id);
                }

                // Если это обычная локация из таблицы locations
                if ($location) {
                    return app(\App\Http\Controllers\Outposts\CustomOutpostController::class)->index($id);
                }

                // Если локация не найдена ни в одной таблице
                abort(404, 'Локация аванпоста не найдена');
            })
                ->middleware(['check.user.health', 'check.user.minimumHP:1,10']) // Изменено с 75% на 10%
                ->where('id', '[0-9]+') // Ограничиваем id только числами
                ->name('show');

            // Маршруты для работы с мобами
            Route::post('/{id}/select-mob/{mobId}', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'selectMob'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('select_mob');
            Route::post('/{id}/select-random-mob', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'selectRandomMob'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('select-random-mob');
            Route::post('/{id}/attack-mob', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attackMob'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('attack_mob');

            // Универсальный маршрут для атаки (для совместимости с компонентами)
            Route::post('/{id}/attack', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attack'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('attack');

            // Маршруты для работы с обелисками
            Route::post('/{id}/contribute-obelisk', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'contributeObelisk'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('contribute-obelisk');

            // Маршруты для работы с игроками
            Route::post('/{id}/select-player/{playerId}', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'selectPlayer'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('select_player');
            Route::post('/{id}/select-bot/{botId}', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'selectBot'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('select_bot');
            Route::post('/{id}/select-ally/{allyId}', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'selectAlly'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('select_ally');
            Route::post('/{id}/attack-player', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attackPlayer'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('attack_player');
            Route::post('/{id}/attack-bot', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attackBot'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('attack_bot');
            Route::post('/{id}/attack-any-player', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attackAnyPlayer'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('attack_any_player');
            Route::post('/{id}/retaliate', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'retaliate'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('retaliate');

            // Маршрут для использования умений
            Route::post('/{id}/use-skill/{skillId}', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'useSkill'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('use_skill');

            // Маршрут для атаки деревни
            Route::post('/{id}/village/{villageId}/attack', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'attackVillage'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('village.attack');

            // Другие маршруты для аванпостов
            Route::post('/{id}/change-target', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'changeTarget'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('change_target');

            // Маршрут для смены цели игрока (для совместимости с компонентами)
            Route::post('/{id}/change_player_target', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'changeTarget'])
                ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                ->name('change_player_target');

            // Маршрут для выхода из локации
            Route::post('/leave-location', [App\Http\Controllers\Outposts\CustomOutpostController::class, 'leaveLocation'])
                ->name('leave-location');

            // Локация "Эльфийская Гавань"
            Route::prefix('elven_haven')->name('elven_haven.')
                ->middleware('check.user.health')
                ->middleware('check.user.minimumHP:1,10') // Проверка на 10% HP только при входе в локацию (изменено с 75%)
                ->middleware('check.user.level:Эльфийская Гавань')
                ->group(function () {
                    Route::get('/', [ElvenHavenController::class, 'index'])->middleware('resetTarget.OnLocationChange')->name('index'); // battle.outposts.elven_haven.index
                    Route::post('/select-mob', [ElvenHavenController::class, 'selectMob'])->name('select_mob'); // battle.outposts.elven_haven.select_mob
                    Route::post('/attack', [ElvenHavenController::class, 'attack'])->name('attack'); // battle.outposts.elven_haven.attack
                    Route::post('/village/{id}/attack', [ElvenHavenController::class, 'attackVillage'])->name('village.attack'); // battle.outposts.elven_haven.attack
    
                    // Для PvP:
                    Route::post('/attack-any', [ElvenHavenController::class, 'attackAnyPlayer'])
                        ->name('attack_any_player');
                    Route::post('/change-target', [ElvenHavenController::class, 'changePlayerTarget'])
                        ->name('change_player_target');
                    Route::post('/retaliate', [ElvenHavenController::class, 'retaliate'])
                        ->name('retaliate');


                    // Добавляем маршрут для умений
                    Route::post('/use-skill/{skillId}', [ElvenHavenController::class, 'useSkill'])
                        ->name('use_skill');
                });

            // Локация "Форт Рассвета"
            Route::prefix('dawn_fort')->name('dawn_fort.')
                ->middleware('check.user.health')
                ->middleware('check.user.minimumHP:1,75') // Проверка на 75% HP только при входе в локацию
                ->middleware('check.user.level:Форт Рассвета')
                ->group(function () {
                    Route::get('/', [DawnFortController::class, 'index'])->middleware('resetTarget.OnLocationChange')->name('index'); // battle.outposts.dawn_fort.index
                    Route::post('/select-mob', [DawnFortController::class, 'selectMob'])->name('select_mob'); // battle.outposts.dawn_fort.select_mob
                    Route::post('/attack', [DawnFortController::class, 'attack'])->name('attack'); // battle.outposts.dawn_fort.attack
                    Route::post('/village/{id}/attack', [DawnFortController::class, 'attackVillage'])->name('village.attack'); // battle.outposts.dawn_fort.village.attack
    
                    // Для PvP:
                    Route::post('/attack-any', [DawnFortController::class, 'attackAnyPlayer'])
                        ->name('attack_any_player');
                    Route::post('/change-target', [DawnFortController::class, 'changePlayerTarget'])
                        ->name('change_player_target');
                    Route::post('/retaliate', [DawnFortController::class, 'retaliate'])
                        ->name('retaliate');

                    // Добавляем маршрут для умений
                    Route::post('/use-skill/{skillId}', [DawnFortController::class, 'useSkill'])
                        ->name('use_skill');
                });

            // Локация "Песчаный Оплот"
            Route::prefix('sandy_stronghold')->name('sandy_stronghold.')
                ->middleware('check.user.health')
                ->middleware('check.user.minimumHP:1,10') // Проверка на 10% HP только при входе в локацию (изменено с 75%)
                ->middleware('check.user.level:Песчаный Оплот')
                ->group(function () {
                    Route::get('/', [SandyStrongholdController::class, 'index'])->middleware('resetTarget.OnLocationChange')->name('index');
                    Route::post('/select-mob', [SandyStrongholdController::class, 'selectMob'])->name('select_mob');
                    Route::post('/attack', [SandyStrongholdController::class, 'attack'])->name('attack');
                    Route::post('/village/{id}/attack', [SandyStrongholdController::class, 'attackVillage'])->name('village.attack');

                    // Для PvP и PvE (атака игроков и ботов)
                    Route::post('/attack-any', [SandyStrongholdController::class, 'attackAnyPlayer'])
                        ->name('attack_any_player'); // Этот маршрут обрабатывает и игроков, и ботов
                    Route::post('/change-target', [SandyStrongholdController::class, 'changePlayerTarget'])
                        ->name('change_player_target');
                    Route::post('/retaliate', [SandyStrongholdController::class, 'retaliate'])
                        ->name('retaliate');

                    // Новый маршрут для выбора бота
                    Route::post('/select-bot', [SandyStrongholdController::class, 'selectBot'])
                        ->name('select_bot');

                    // Добавляем маршрут для умений
                    Route::post('/use-skill/{skillId}', [SandyStrongholdController::class, 'useSkill'])
                        ->name('use_skill');
                });
        });

        // Группа для рудников
        Route::prefix('mines')->name('mines.')->group(function () {
            Route::get('/', [MinesController::class, 'index'])->name('index'); // battle.mines.index

            // API маршруты для фильтрации рудников
            Route::prefix('api')->name('api.')->group(function () {
                Route::get('/resources', [\App\Http\Controllers\API\MineFilterController::class, 'getResources'])->name('resources');
                Route::get('/items', [\App\Http\Controllers\API\MineFilterController::class, 'getItems'])->name('items');
                Route::get('/item-types', [\App\Http\Controllers\API\MineFilterController::class, 'getItemTypes'])->name('item-types');
                Route::get('/item-qualities', [\App\Http\Controllers\API\MineFilterController::class, 'getItemQualities'])->name('item-qualities');
                Route::post('/filter', [\App\Http\Controllers\API\MineFilterController::class, 'filterMines'])->name('filter');
            });

            // Маршруты для пользовательских шахт
            Route::name('custom.')->group(function () {
                // Основной маршрут для отображения локации
                Route::get('/{slug}', [CustomMineController::class, 'index'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,75'])
                    ->name('index');

                // Маршруты для работы с ресурсами
                Route::post('/{slug}/select-resource/{id}', [CustomMineController::class, 'selectResource'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('select-resource');
                Route::post('/{slug}/hit-resource', [CustomMineController::class, 'hitResource'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('hit-resource');

                // Маршруты для работы с мобами
                Route::post('/{slug}/select-mob/{id}', [CustomMineController::class, 'selectMob'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('select-mob');
                Route::post('/{slug}/attack-mob', [CustomMineController::class, 'attackMob'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('attack-mob');

                // Маршруты для работы с ботами
                Route::post('/{slug}/select-bot/{id}', [CustomMineController::class, 'selectBot'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('select-bot');
                Route::post('/{slug}/attack-bot', [CustomMineController::class, 'attackBot'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('attack-bot');

                // Маршруты для работы с игроками
                Route::post('/{slug}/attack-any-player', [CustomMineController::class, 'attackAnyPlayer'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('attack-any-player');
                Route::post('/{slug}/attack-player', [CustomMineController::class, 'attackPlayer'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('attack-player');
                Route::post('/{slug}/retaliate', [CustomMineController::class, 'retaliate'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('retaliate');

                // Маршрут для использования умений
                Route::post('/{slug}/use-skill/{skillId}', [CustomMineController::class, 'useSkill'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('use_skill');

                // Маршрут для смены цели
                Route::post('/{slug}/change-target', [CustomMineController::class, 'changeTarget'])
                    ->middleware(['check.user.health', 'check.user.minimumHP:1,0'])
                    ->name('change_target');

                // Маршрут для выхода из локации (НЕ ДОЛЖЕН ИСПОЛЬЗОВАТЬСЯ - только для совместимости)
                Route::post('/leave-location', function () {
                    // Редирект на карту, так как в шахтах нет функции "покинуть локацию"
                    return redirect()->route('map')->with('info', 'Вы покинули шахту');
                })->name('leave-location');
            });





        });


    });
    // Онлайн пользователи
    Route::get('/online', [UserController::class, 'showOnlineUsers'])->name('online.users'); // /online

    // Русский комментарий: Система пожертвований для поддержки проекта
    // Маршрут для страницы пожертвований - статическая страница с информацией о способах поддержки
    Route::get('/donations', [App\Http\Controllers\DonationController::class, 'index'])->name('donations.index');

    // Примеры использования компонентов
    Route::prefix('examples')->name('examples.')->group(function () {
        Route::get('/use-potions', [App\Http\Controllers\Examples\UsePotionsExampleController::class, 'index'])->name('use-potions');
        Route::get('/quick-potion-bar', [App\Http\Controllers\Examples\QuickPotionBarExampleController::class, 'index'])->name('quick-potion-bar');
    });

    // Маршруты для работы с зельями
    Route::post('/use-potion/{id}', [App\Http\Controllers\PotionController::class, 'usePotion'])->name('use-potion');

    // Группировка маршрутов для профессий
    Route::prefix('professions')->name('professions.')->group(function () {
        // Главная страница профессий
        Route::get('/', [App\Http\Controllers\Professions\ProfessionsController::class, 'index'])->name('index');

        // Маршруты для отдельных профессий
        Route::prefix('cooking')->name('cooking.')->group(function () {
            Route::get('/', [App\Http\Controllers\Professions\CookingController::class, 'index'])->name('index');
            Route::post('/craft', [App\Http\Controllers\Professions\CookingController::class, 'craft'])->name('craft');
        });

        Route::prefix('carpentry')->name('carpentry.')->group(function () {
            Route::get('/', [App\Http\Controllers\Professions\CarpentryController::class, 'index'])->name('index');
            Route::post('/craft', [App\Http\Controllers\Professions\CarpentryController::class, 'craft'])->name('craft');
        });

        Route::prefix('tailoring')->name('tailoring.')->group(function () {
            Route::get('/', [App\Http\Controllers\Professions\TailoringController::class, 'index'])->name('index');
            Route::post('/craft', [App\Http\Controllers\Professions\TailoringController::class, 'craft'])->name('craft');
        });
    });

    // Маршруты для гильдий
    Route::prefix('guilds')->name('guilds.')->group(function () {
        Route::get('/', [GuildController::class, 'index'])->name('index');
        Route::get('/create', [GuildController::class, 'create'])->name('create');
        Route::post('/store', [GuildController::class, 'store'])->name('store');
        Route::get('/{guild}', [GuildController::class, 'show'])->name('show');
        Route::get('/{guild}/settings', [GuildController::class, 'settings'])->name('settings');
        Route::get('/{guild}/change-icon', [GuildController::class, 'changeIcon'])->name('change-icon');
        Route::post('/{guild}/update-icon', [GuildController::class, 'updateIcon'])->name('update-icon');
        Route::post('/{guild}/leave', [GuildController::class, 'leave'])->name('leave');
        Route::post('/{guild}/invite/{user}', [GuildController::class, 'invite'])->name('invite');
        Route::post('/invitations/{invitation}/accept', [GuildController::class, 'acceptInvitation'])->name('invitations.accept');
        Route::post('/invitations/{invitation}/decline', [GuildController::class, 'declineInvitation'])->name('invitations.decline');

        // Маршруты для построек гильдии
        Route::prefix('{guild}/buildings')->name('buildings.')->group(function () {
            Route::get('/', [GuildBuildingController::class, 'index'])->name('index');
            Route::post('/{building}/activate', [GuildBuildingController::class, 'activate'])->name('activate');
            Route::post('/{building}/upgrade', [GuildBuildingController::class, 'upgrade'])->name('upgrade');
        });
    });

    // Маршрут для топа игроков-тестировщиков
    Route::get('/test-top', [App\Http\Controllers\TestTopController::class, 'index'])->name('test-top');

    // Маршруты для топа игроков вынесены в отдельный файл routes/top-players.php
    require __DIR__ . '/top-players.php';

    // Группировка маршрутов для фермерства
    Route::prefix('farming')->middleware(['auth'])->name('farming.')->group(function () {
        Route::get('/', [FarmingController::class, 'index'])->name('index');
        Route::get('/beds', [BedsController::class, 'index'])->name('beds');
        Route::get('/seeds', [SeedsController::class, 'index'])->name('seeds');
        Route::get('/fertilizers', [FertilizersController::class, 'index'])->name('fertilizers');
        Route::get('/workers', [WorkersController::class, 'index'])->name('workers');

        // Маршрут для выбора семян для посадки на грядку
        Route::get('/beds/{bedId}/select-seed', [App\Http\Controllers\Farming\SeedSelectionController::class, 'index'])->name('seed-selection');

        Route::post('/beds/{bedId}/plant', [BedsController::class, 'plantSeed'])->name('beds.plant');
        Route::post('/beds/{bedId}/water', [BedsController::class, 'waterPlant'])->name('water-plant');
        Route::post('/beds/{bedId}/fertilize', [BedsController::class, 'fertilizePlant'])->name('fertilize-plant');
        Route::post('/beds/{bedId}/harvest', [BedsController::class, 'harvestPlant'])->name('harvest-plant');
        Route::post('/beds/{bedId}/unlock', [BedsController::class, 'unlockBed'])->name('unlock-bed'); // Новый маршрут
        Route::post('/beds/{bedId}/cancel', [App\Http\Controllers\Farming\BedsController::class, 'cancelGrowing'])->name('beds.cancel');

        // Маршруты для работников
        Route::get('/workers', [WorkersController::class, 'index'])->name('workers');
        Route::post('/workers/{workerId}/hire', [WorkersController::class, 'hireWorker'])->name('workers.hire');
        Route::post('/workers/{workerId}/fire', [WorkersController::class, 'fireWorker'])->name('workers.fire');

        // Исправленный маршрут для улучшения семян
        Route::post('/seeds/upgrade', [SeedsController::class, 'upgradeSeed'])->name('seeds.upgrade');

        // В группе маршрутов farming.seeds
        Route::post('/merge', [SeedsController::class, 'mergeSeed'])->name('merge');

        // Маршруты для склада урожая
        Route::get('/storage', [App\Http\Controllers\Farming\StorageController::class, 'index'])->name('storage');
        Route::post('/storage/action', [App\Http\Controllers\Farming\StorageController::class, 'processAction'])->name('storage.action');
        Route::post('/storage/move-to-storage', [App\Http\Controllers\Farming\StorageController::class, 'moveToStorage'])->name('storage.move-to-storage');
        Route::post('/storage/expand-sections', [App\Http\Controllers\Farming\StorageController::class, 'expandSections'])->name('storage.expand-sections');
        Route::post('/storage/expand-capacity', [App\Http\Controllers\Farming\StorageController::class, 'expandCapacity'])->name('storage.expand-capacity');

        // Маршруты для амбара (улучшение вместимости секций для конкретных типов культур)
        Route::get('/barn', [App\Http\Controllers\Farming\BarnController::class, 'index'])->name('barn');
        Route::post('/barn/upgrade-capacity', [App\Http\Controllers\Farming\BarnController::class, 'upgradeCapacity'])->name('barn.upgrade');
    });

    // Маршруты для работы с семенами
    Route::prefix('farming/seeds')->name('farming.seeds.')->middleware('auth')->group(function () {
        Route::post('/action', [SeedsController::class, 'processAction'])->name('action');
        Route::get('/transfer/{seed_id}', [SeedsController::class, 'transferForm'])->name('transfer');
        Route::post('/transfer', [SeedsController::class, 'transferSeed'])->name('transfer.process');

        // Маршрут для улучшения семян
        Route::post('/upgrade', [SeedsController::class, 'upgradeSeed'])->name('upgrade');

        // Прямой доступ к методу merge (для тестирования)
        Route::post('/merge-direct', [SeedsController::class, 'mergeSeed'])->name('merge.direct');
    });

    // Маршруты для чата
    Route::get('/chat', [ChatController::class, 'index'])->name('chat.index');
    Route::match(['get', 'post'], '/chat/get-messages', [ChatController::class, 'getMessages'])->name('chat.getMessages');
    Route::post('/chat/send', [ChatController::class, 'sendMessage'])->name('chat.send');

    // Новый маршрут для получения количества игроков в локации
    Route::post('/chat/get-players-count', [ChatController::class, 'getPlayersCount'])->name('chat.getPlayersCount');

    // Новый маршрут для получения количества непрочитанных сообщений для всех каналов
    Route::post('/chat/get-all-unread', [ChatController::class, 'getAllUnreadCounts'])->middleware(['auth'])->name('chat.getAllUnreadCounts');

    // Маршруты для личных сообщений
    Route::prefix('messages')->name('messages.')->group(function () {
        Route::get('/', [MessageController::class, 'index'])->name('index');
        Route::get('/create/{userId?}', [MessageController::class, 'create'])->name('create');
        Route::post('/store', [MessageController::class, 'store'])->name('store');
        Route::get('/{id}', [MessageController::class, 'show'])->name('show');
        Route::post('/{id}/clear', [MessageController::class, 'clearConversation'])->name('clear');
        Route::post('/{id}/delete', [MessageController::class, 'deleteConversation'])->name('delete');
    });

    // Маршруты для уведомлений
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/mark-read/{id}', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::get('/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::get('/delete/{id}', [App\Http\Controllers\NotificationController::class, 'delete'])->name('delete');
        Route::get('/delete-read', [App\Http\Controllers\NotificationController::class, 'deleteAllRead'])->name('delete-read');
    });
});

// Группировка маршрутов для форума
Route::prefix('forum')->name('forum.')->middleware(['auth', 'user.activity'])->group(function () {
    // Главная страница форума
    Route::get('/', [App\Http\Controllers\Forum\IndexController::class, 'index'])->name('index');

    // Категории форума
    Route::get('/category/{id}', [App\Http\Controllers\Forum\IndexController::class, 'category'])->name('category');

    // Маршруты для тем
    Route::get('/topic/{id}', [App\Http\Controllers\Forum\TopicController::class, 'show'])->name('topic.show');
    Route::get('/topic/create/{categoryId}', [App\Http\Controllers\Forum\TopicController::class, 'create'])->name('topic.create');
    Route::post('/topic/store/{categoryId}', [App\Http\Controllers\Forum\TopicController::class, 'store'])->name('topic.store');

    // Маршруты для сообщений (постов)
    Route::middleware(['bbcode.permission'])->group(function () {
        Route::get('/post/{id}/edit', [App\Http\Controllers\Forum\PostController::class, 'edit'])->name('post.edit');
        Route::put('/post/{id}', [App\Http\Controllers\Forum\PostController::class, 'update'])->name('post.update');
        Route::delete('/post/{id}', [App\Http\Controllers\Forum\PostController::class, 'destroy'])->name('post.destroy');
        Route::post('/topic/{topicId}/post', [App\Http\Controllers\Forum\PostController::class, 'store'])->name('post.store');
        Route::get('/post/{id}/mention', [App\Http\Controllers\Forum\PostController::class, 'quote'])->name('post.mention');
    });

    // Новости
    Route::prefix('news')->name('news.')->group(function () {
        Route::get('/', [NewsController::class, 'index'])->name('index');
        Route::get('/show/{id}', [NewsController::class, 'show'])->name('show');

        // Маршруты для администраторов и модераторов
        Route::middleware(['auth', 'bbcode.permission'])->group(function () {
            Route::get('/create', [NewsController::class, 'create'])->middleware('can:create,App\Models\Forum\News')->name('create');
            Route::post('/store', [NewsController::class, 'store'])->middleware('can:create,App\Models\Forum\News')->name('store');
            Route::get('/edit/{id}', [NewsController::class, 'edit'])->middleware('can:update,App\Models\Forum\News')->name('edit');
            Route::put('/update/{id}', [NewsController::class, 'update'])->middleware('can:update,App\Models\Forum\News')->name('update');
            Route::delete('/destroy/{id}', [NewsController::class, 'destroy'])->middleware('can:delete,App\Models\Forum\News')->name('destroy');
        });

        // Комментарии к новостям
        Route::post('/{newsId}/comments', [NewsCommentController::class, 'store'])->name('comments.store');
        Route::delete('/comments/{commentId}', [NewsCommentController::class, 'destroy'])->name('comments.destroy');
        Route::get('/comments/{commentId}/edit', [NewsCommentController::class, 'edit'])->name('comments.edit');
        Route::put('/comments/{commentId}', [NewsCommentController::class, 'update'])->name('comments.update');

        // Реакции (лайки/дизлайки)
        Route::post('/{newsId}/like', [NewsReactionController::class, 'like'])->name('like');
        Route::post('/{newsId}/dislike', [NewsReactionController::class, 'dislike'])->name('dislike');
    });

    Route::delete('/topic/{id}', [App\Http\Controllers\Forum\TopicController::class, 'destroy'])
        ->middleware('can:delete,App\Models\Forum\Topic')
        ->name('topic.destroy');


});

// Тестовый маршрут для просмотра отформатированного шаблона
Route::get('/test-sample', [App\Http\Controllers\TestSampleController::class, 'index'])
    ->name('test.sample');

// Маршруты для системы тикетов (для авторизованных пользователей)
Route::middleware(['auth'])->group(function () {
    Route::get('/tickets', [App\Http\Controllers\TicketController::class, 'index'])->name('tickets.index');
    Route::get('/tickets/create', [App\Http\Controllers\TicketController::class, 'create'])->name('tickets.create');
    Route::post('/tickets', [App\Http\Controllers\TicketController::class, 'store'])->name('tickets.store');
    Route::get('/tickets/{ticket}', [App\Http\Controllers\TicketController::class, 'show'])->name('tickets.show');
    Route::post('/tickets/{ticket}/comment', [App\Http\Controllers\TicketController::class, 'addComment'])->name('tickets.comment');
});

// Группировка маршрутов админки

Route::middleware(['auth', AdminMiddleware::class, 'upload.limits'])
    ->prefix('admin')
    ->name('admin.')
    ->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

        // Административные маршруты для тикетов
        Route::get('/tickets', [App\Http\Controllers\Admin\TicketController::class, 'index'])->name('tickets.index');
        Route::get('/tickets/{ticket}', [App\Http\Controllers\Admin\TicketController::class, 'show'])->name('tickets.show');
        Route::patch('/tickets/{ticket}/status', [App\Http\Controllers\Admin\TicketController::class, 'updateStatus'])->name('tickets.update-status');
        Route::post('/tickets/{ticket}/comment', [App\Http\Controllers\Admin\TicketController::class, 'addComment'])->name('tickets.comment');
        Route::post('/tickets/{ticket}/attachments', [App\Http\Controllers\Admin\TicketController::class, 'addAttachments'])->name('tickets.add-attachments');

        // Редактирование и удаление комментариев администрации
        Route::put('/tickets/comments/{comment}', [App\Http\Controllers\Admin\TicketController::class, 'updateComment'])->name('tickets.update-comment');
        Route::delete('/tickets/comments/{comment}', [App\Http\Controllers\Admin\TicketController::class, 'deleteComment'])->name('tickets.delete-comment');
        Route::delete('/tickets/attachments/{attachment}', [App\Http\Controllers\Admin\TicketController::class, 'deleteAttachment'])->name('tickets.delete-attachment');
        Route::get('/tickets/create-by-user/{userId}', [App\Http\Controllers\Admin\TicketController::class, 'createByUserId'])->name('tickets.create-by-user');

        // Административные маршруты для управления тестировщиками
        Route::get('/testers', [App\Http\Controllers\Admin\TesterController::class, 'index'])->name('testers.index');
        Route::get('/testers/{user}', [App\Http\Controllers\Admin\TesterController::class, 'show'])->name('testers.show');
        Route::get('/testers/{user}/create-ticket', [App\Http\Controllers\Admin\TesterController::class, 'createTicket'])->name('testers.create-ticket');
        Route::post('/testers/{user}/store-ticket', [App\Http\Controllers\Admin\TesterController::class, 'storeTicket'])->name('testers.store-ticket');

        // Маршруты для управления обелисками
        Route::resource('obelisks', ObeliskController::class);
        Route::post('/obelisks/sync', [ObeliskController::class, 'syncObelisksWithLocations'])->name('obelisks.sync');
        Route::post('/obelisks/sync-progress', [ObeliskController::class, 'syncObelisksWithProgress'])->name('obelisks.sync-progress');
        Route::resource('items', ItemController::class)->except(['show']); // Исключаем show
        Route::get('/items/list', [ItemController::class, 'list'])->name('items.list'); // Список предметов
        Route::get('/items/create', [ItemController::class, 'create'])->name('items.create'); // Форма создания
        Route::delete('/items/{item}/delete-all-instances', [ItemController::class, 'deleteAllInstances'])->name('items.delete-all-instances'); // Массовое удаление экземпляров
        Route::delete('/items/{item}/delete-all-instances-from-all-players', [ItemController::class, 'deleteAllInstancesFromAllPlayers'])->name('items.delete-all-instances-from-all-players'); // Массовое удаление экземпляров у всех игроков
    
        // Маршруты для управления локациями
        Route::resource('locations', LocationController::class)->where(['location' => '[0-9]+']);
        Route::put('/admin/items/{id}', [ItemController::class, 'update'])->name('admin.items.update');

        // Маршруты для управления ресурсами локаций
        Route::resource('location-resources', \App\Http\Controllers\Admin\LocationResourceController::class);
        Route::post('location-resources/respawn-all', [\App\Http\Controllers\Admin\LocationResourceController::class, 'respawnAll'])->name('location-resources.respawn-all');
        Route::post('location-resources/generate', [\App\Http\Controllers\Admin\LocationResourceController::class, 'generateResources'])->name('location-resources.generate');
        Route::post('location-resources/delete-all', [\App\Http\Controllers\Admin\LocationResourceController::class, 'deleteAll'])->name('location-resources.delete-all');

        // Маршруты для калькулятора статов предметов
        Route::get('/items/stats-calculator', [ItemController::class, 'statsCalculator'])->name('items.stats-calculator');
        Route::post('/items/calculate-stats', [ItemController::class, 'calculateStats'])->name('items.calculate-stats');

        // Маршруты для управления предметами в магазине
        // Routes for managing shop items
        Route::resource('shop', ShopAdminController::class)->parameters(['shop' => 'shopItem']);
        // parameters(['shop' => 'shopItem']) используется для явного указания имени параметра
        // в маршруте для Route Model Binding, чтобы он совпадал с именем переменной в методах контроллера.
    
        // Маршруты для управления настройками рынка
        Route::get('/market-settings', [App\Http\Controllers\Admin\MarketSettingsController::class, 'index'])->name('market-settings.index');
        Route::post('/market-settings', [App\Http\Controllers\Admin\MarketSettingsController::class, 'update'])->name('market-settings.update');
        Route::get('/market-settings/user-slots', [App\Http\Controllers\Admin\MarketSettingsController::class, 'userSlots'])->name('market-settings.user-slots');
        Route::put('/market-settings/user-slots/{id}', [App\Http\Controllers\Admin\MarketSettingsController::class, 'updateUserSlots'])->name('market-settings.user-slots.update');

        // Маршруты для управления пользователями
        Route::resource('users', \App\Http\Controllers\Admin\UserAdminController::class);
        // Маршрут для просмотра предметов пользователя
        Route::get('/users/{userId}/items', [\App\Http\Controllers\Admin\UserAdminController::class, 'userItems'])->name('users.items');
        // Маршрут-заглушка для квестов пользователя (будет реализован позже)
        Route::get('/users/{userId}/quests', [\App\Http\Controllers\Admin\UserAdminController::class, 'userQuests'])->name('users.quests');
        // Маршрут-заглушка для сброса пароля пользователя (будет реализован позже)
        Route::post('/users/{userId}/reset-password', [\App\Http\Controllers\Admin\UserAdminController::class, 'resetPassword'])->name('users.reset_password');
        Route::post('/users/{id}/ban', [\App\Http\Controllers\Admin\UserAdminController::class, 'ban'])->name('users.ban');
        Route::post('/users/{id}/unban', [\App\Http\Controllers\Admin\UserAdminController::class, 'unban'])->name('users.unban');
        Route::put('/users/{id}/update-profile', [\App\Http\Controllers\Admin\UserAdminController::class, 'updateProfile'])->name('users.update_profile');
        // Маршрут для обновления статов пользователя (HP, сила, ловкость и т.д.)
        Route::put('/users/{id}/update-stats', [\App\Http\Controllers\Admin\UserAdminController::class, 'updateStats'])->name('users.update_stats');

        // Маршруты для управления сообщениями
        Route::resource('messages', \App\Http\Controllers\Admin\MessageController::class);

        // Маршруты для управления удаленными сообщениями
        Route::get('/deleted-messages', [\App\Http\Controllers\Admin\DeletedMessagesController::class, 'index'])->name('deleted-messages.index');
        Route::get('/deleted-messages/{id}', [\App\Http\Controllers\Admin\DeletedMessagesController::class, 'show'])->name('deleted-messages.show');

        // Добавляем маршруты для управления мобами
        Route::resource('mobs', \App\Http\Controllers\Admin\MobController::class);

        // Маршрут для тестирования дропа ресурсов
        Route::get('/mobs/{mobId}/test-resource-drop/{iterations?}', [\App\Http\Controllers\Admin\MobController::class, 'testResourceDrop'])
            ->name('mobs.test_resource_drop');

        // Маршруты для управления скиллами мобов
        Route::resource('mob-skills', \App\Http\Controllers\Admin\MobSkillController::class);
        Route::get('/mob-skills-assign', [\App\Http\Controllers\Admin\MobSkillController::class, 'assignToMob'])
            ->name('mob-skills.assign');
        Route::post('/mob-skills-attach', [\App\Http\Controllers\Admin\MobSkillController::class, 'attachToMob'])
            ->name('mob-skills.attach');
        Route::delete('/mob-skills-detach', [\App\Http\Controllers\Admin\MobSkillController::class, 'detachFromMob'])
            ->name('mob-skills.detach');

        // Маршруты для управления алхимией
        Route::get('/alchemy', [\App\Http\Controllers\Admin\AlchemyController::class, 'index'])->name('alchemy.index');

        // Новые маршруты для автономных страниц алхимии
        Route::get('/alchemy/standalone', [\App\Http\Controllers\Admin\AlchemyController::class, 'index'])
            ->name('alchemy.standalone')
            ->defaults('standalone', true);

        Route::get('/alchemy/analytics', [\App\Http\Controllers\Admin\AlchemyController::class, 'analytics'])
            ->name('alchemy.analytics');

        Route::get('/alchemy/analytics/standalone', [\App\Http\Controllers\Admin\AlchemyController::class, 'analytics'])
            ->name('alchemy.analytics.standalone')
            ->defaults('standalone', true);

        Route::get('/alchemy/ingredients/standalone', [\App\Http\Controllers\Admin\AlchemyController::class, 'ingredientsStandalone'])
            ->name('alchemy.ingredients.standalone');

        Route::get('/alchemy/recipes/standalone', [\App\Http\Controllers\Admin\AlchemyController::class, 'recipesStandalone'])
            ->name('alchemy.recipes.standalone');

        Route::get('/alchemy/catalysts/standalone', [\App\Http\Controllers\Admin\AlchemyController::class, 'catalystsStandalone'])
            ->name('alchemy.catalysts.standalone');

        // Маршруты для управления рецептами зелий
        Route::resource('potion-recipes', \App\Http\Controllers\Admin\PotionRecipeController::class);

        // Маршруты для выдачи ингредиентов игрокам (должны быть перед ресурсным маршрутом)
        Route::get('/alchemy-ingredients/give', [\App\Http\Controllers\Admin\AlchemyIngredientController::class, 'showGiveForm'])
            ->name('alchemy-ingredients.give');
        Route::post('/alchemy-ingredients/give-to-player', [\App\Http\Controllers\Admin\AlchemyIngredientController::class, 'giveToPlayer'])
            ->name('alchemy-ingredients.give-to-player');

        // Маршруты для управления ингредиентами алхимии
        Route::resource('alchemy-ingredients', \App\Http\Controllers\Admin\AlchemyIngredientController::class);

        // Маршруты для управления катализаторами
        Route::resource('alchemy-catalysts', \App\Http\Controllers\Admin\AlchemyCatalystController::class);

        // Маршруты для управления зельями
        Route::resource('potions', \App\Http\Controllers\Admin\PotionController::class);

        // Маршрут для создания тестового зелья на основе рецепта
        Route::post('/potions/create-test', [App\Http\Controllers\Admin\PotionController::class, 'createTestPotion'])
            ->name('potions.create-test');

        // Маршрут для создания игрового предмета на основе шаблона зелья
        Route::post('/potions/{id}/create-game-item', [App\Http\Controllers\Admin\PotionController::class, 'createGameItemFromPotion'])
            ->name('potions.create-game-item');

        // Маршруты для управления ботами
        // Кастомные маршруты должны быть ПЕРЕД ресурсным маршрутом
        Route::delete('/bots/destroy-all', [BotController::class, 'destroyAll'])->name('bots.destroy-all');
        Route::post('/bots/{bot}/respawn', [BotController::class, 'respawn'])->name('bots.respawn');

        Route::resource('bots', BotController::class);

        // Маршруты для управления обелисками
        Route::resource('obelisks', ObeliskController::class);
        Route::get('/obelisks/{obelisk}/drop-settings', [ObeliskController::class, 'dropSettings'])->name('obelisks.drop-settings');
        Route::post('/obelisks/{obelisk}/drop-settings', [ObeliskController::class, 'saveDropSettings'])->name('obelisks.drop-settings.save');
        Route::post('/obelisks/{obelisk}/reset', [ObeliskController::class, 'resetProgress'])->name('obelisks.reset');
        Route::post('/obelisks/{obelisk}/drop/add', [ObeliskController::class, 'addDropItem'])->name('obelisks.drop.add');
        Route::delete('/obelisks/drop/{obeliskDrop}', [ObeliskController::class, 'removeDropItem'])->name('obelisks.drop.remove');

        // Маршруты для управления предметами игроков
        Route::get('/player-items', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'index'])->name('player-items.index');
        Route::get('/player-items/player/{userId}', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'playerItems'])->name('player-items.player');
        Route::get('/player-items/edit/{id}', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'edit'])->name('player-items.edit');
        Route::put('/player-items/update/{id}', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'update'])->name('player-items.update');
        Route::delete('/player-items/destroy/{id}', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'destroy'])->name('player-items.destroy');
        Route::get('/player-items/search', [\App\Http\Controllers\Admin\PlayerItemsController::class, 'search'])->name('player-items.search');

        // Маршруты для управления дропами предметов у мобов
        Route::get('mobs/{mobId}/item-drops', [App\Http\Controllers\Admin\MobItemDropController::class, 'index'])
            ->name('admin.mob-item-drops.index');
        Route::get('mobs/{mobId}/item-drops/create', [App\Http\Controllers\Admin\MobItemDropController::class, 'create'])
            ->name('admin.mob-item-drops.create');
        Route::post('mobs/{mobId}/item-drops', [App\Http\Controllers\Admin\MobItemDropController::class, 'store'])
            ->name('admin.mob-item-drops.store');
        Route::get('mobs/{mobId}/item-drops/{dropId}/edit', [App\Http\Controllers\Admin\MobItemDropController::class, 'edit'])
            ->name('admin.mob-item-drops.edit');
        Route::put('mobs/{mobId}/item-drops/{dropId}', [App\Http\Controllers\Admin\MobItemDropController::class, 'update'])
            ->name('admin.mob-item-drops.update');
        Route::delete('mobs/{mobId}/item-drops/{dropId}', [App\Http\Controllers\Admin\MobItemDropController::class, 'destroy'])
            ->name('admin.mob-item-drops.destroy');
        Route::get('mobs/{mobId}/item-drops/test/{iterations?}', [App\Http\Controllers\Admin\MobItemDropController::class, 'testItemDrop'])
            ->name('admin.mob-item-drops.test');

        // Добавьте эти маршруты в конец группы
        Route::get('/mobs/{mobId}/alchemy-drops', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'index'])
            ->name('admin.mob-alchemy-drops.index');
        Route::get('/mobs/{mobId}/alchemy-drops/create', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'create'])
            ->name('admin.mob-alchemy-drops.create');
        Route::post('/mobs/{mobId}/alchemy-drops', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'store'])
            ->name('admin.mob-alchemy-drops.store');
        Route::get('/mobs/{mobId}/alchemy-drops/{dropId}/edit', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'edit'])
            ->name('admin.mob-alchemy-drops.edit');
        Route::put('/mobs/{mobId}/alchemy-drops/{dropId}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'update'])
            ->name('admin.mob-alchemy-drops.update');
        Route::delete('/mobs/{mobId}/alchemy-drops/{dropId}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'destroy'])
            ->name('admin.mob-alchemy-drops.destroy');
        Route::get('/mobs/{mobId}/alchemy-drops/test/{iterations?}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'testAlchemyIngredientDrop'])
            ->name('admin.mob-alchemy-drops.test');
    });


// СТАРЫЕ НЕЗАЩИЩЕННЫЕ МАРШРУТЫ АУТЕНТИФИКАЦИИ УДАЛЕНЫ ДЛЯ БЕЗОПАСНОСТИ
// Теперь все маршруты аутентификации требуют действительный ключ доступа
// и находятся в группе /auth/* выше

// Маршрут выхода остается доступным для авторизованных пользователей
Route::post('/logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Роуты для управления ресурсными дропами мобов (админка)
Route::group(['prefix' => 'admin', 'middleware' => ['auth', AdminMiddleware::class, 'upload.limits']], function () {
    Route::get('/mobs/{mobId}/resource-drops', [MobResourceDropController::class, 'index'])
        ->name('admin.mob-resource-drops.index');
    Route::get('/mobs/{mobId}/resource-drops/create', [MobResourceDropController::class, 'create'])
        ->name('admin.mob-resource-drops.create');
    Route::post('/mobs/{mobId}/resource-drops', [MobResourceDropController::class, 'store'])
        ->name('admin.mob-resource-drops.store');
    Route::get('/mobs/{mobId}/resource-drops/{dropId}/edit', [MobResourceDropController::class, 'edit'])
        ->name('admin.mob-resource-drops.edit');
    Route::put('/mobs/{mobId}/resource-drops/{dropId}', [MobResourceDropController::class, 'update'])
        ->name('admin.mob-resource-drops.update');
    Route::delete('/mobs/{mobId}/resource-drops/{dropId}', [MobResourceDropController::class, 'destroy'])
        ->name('admin.mob-resource-drops.destroy');

    // Тестирование дропа
    Route::get('/mobs/{mobId}/test-resource-drop/{iterations?}', [MobController::class, 'testResourceDrop'])
        ->name('admin.mobs.test-resource-drop');

    // Роуты для управления валютными дропами мобов (админка)
    Route::get('/mobs/{mobId}/currency-drops', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'index'])
        ->name('admin.mob-currency-drops.index');
    Route::get('/mobs/{mobId}/currency-drops/create', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'create'])
        ->name('admin.mob-currency-drops.create');
    Route::post('/mobs/{mobId}/currency-drops', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'store'])
        ->name('admin.mob-currency-drops.store');
    Route::get('/mobs/{mobId}/currency-drops/{dropId}/edit', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'edit'])
        ->name('admin.mob-currency-drops.edit');
    Route::put('/mobs/{mobId}/currency-drops/{dropId}', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'update'])
        ->name('admin.mob-currency-drops.update');
    Route::delete('/mobs/{mobId}/currency-drops/{dropId}', [App\Http\Controllers\Admin\MobCurrencyDropController::class, 'destroy'])
        ->name('admin.mob-currency-drops.destroy');

    // Роуты для управления предметными дропами мобов (админка)
    Route::get('/mobs/{mobId}/item-drops', [App\Http\Controllers\Admin\MobItemDropController::class, 'index'])
        ->name('admin.mob-item-drops.index');
    Route::get('/mobs/{mobId}/item-drops/create', [App\Http\Controllers\Admin\MobItemDropController::class, 'create'])
        ->name('admin.mob-item-drops.create');
    Route::post('/mobs/{mobId}/item-drops', [App\Http\Controllers\Admin\MobItemDropController::class, 'store'])
        ->name('admin.mob-item-drops.store');
    Route::get('/mobs/{mobId}/item-drops/{dropId}/edit', [App\Http\Controllers\Admin\MobItemDropController::class, 'edit'])
        ->name('admin.mob-item-drops.edit');
    Route::put('/mobs/{mobId}/item-drops/{dropId}', [App\Http\Controllers\Admin\MobItemDropController::class, 'update'])
        ->name('admin.mob-item-drops.update');
    Route::delete('/mobs/{mobId}/item-drops/{dropId}', [App\Http\Controllers\Admin\MobItemDropController::class, 'destroy'])
        ->name('admin.mob-item-drops.destroy');
    Route::get('/mobs/{mobId}/test-item-drop/{iterations?}', [App\Http\Controllers\Admin\MobItemDropController::class, 'testItemDrop'])
        ->name('admin.mob-item-drops.test');

    // Роуты для управления дропами алхимических ингредиентов у мобов
    Route::get('/mobs/{mobId}/alchemy-drops', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'index'])
        ->name('admin.mob-alchemy-drops.index');
    Route::get('/mobs/{mobId}/alchemy-drops/create', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'create'])
        ->name('admin.mob-alchemy-drops.create');
    Route::post('/mobs/{mobId}/alchemy-drops', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'store'])
        ->name('admin.mob-alchemy-drops.store');
    Route::get('/mobs/{mobId}/alchemy-drops/{dropId}/edit', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'edit'])
        ->name('admin.mob-alchemy-drops.edit');
    Route::put('/mobs/{mobId}/alchemy-drops/{dropId}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'update'])
        ->name('admin.mob-alchemy-drops.update');
    Route::delete('/mobs/{mobId}/alchemy-drops/{dropId}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'destroy'])
        ->name('admin.mob-alchemy-drops.destroy');
    Route::get('/mobs/{mobId}/alchemy-drops/test/{iterations?}', [App\Http\Controllers\Admin\MobAlchemyIngredientDropController::class, 'testAlchemyIngredientDrop'])
        ->name('admin.mob-alchemy-drops.test');
});

// Elven Haven battle (URL с дефисом)
// Восстановлено с другим именем для избежания конфликта
Route::prefix('battle/outposts/elven-haven')->name('battle.outposts.elven_haven_dash.')->middleware(['auth'])->group(function () {
    Route::get('/', [ElvenHavenController::class, 'index'])->name('index');
    Route::post('/attack', [ElvenHavenController::class, 'attack'])->name('attack');
    Route::post('/change-target', [ElvenHavenController::class, 'changeTarget'])->name('change_target');
    Route::post('/select-bot', [ElvenHavenController::class, 'selectBot'])->name('select_bot'); // Новый маршрут для выбора бота
});



// Подключаем маршруты для админ-панели кузнеца
require __DIR__ . '/admin/blacksmith.php';

// Подключаем маршруты для админ-панели подземелий
require __DIR__ . '/admin/dungeons.php';

// Подключаем маршруты для управления ключами доступа в админ-панели
require __DIR__ . '/admin/access-keys.php';

// Импортируем контроллер для управления семенами в админке
use App\Http\Controllers\Admin\FarmingSeedController;

// Импортируем контроллер для управления урожаем в админке
use App\Http\Controllers\Admin\HarvestController;

// Импортируем контроллер для управления шансами выпадения урожая
use App\Http\Controllers\Admin\HarvestChanceController;

// Маршруты для управления семенами и урожаем в админке
Route::middleware(['auth', AdminMiddleware::class, 'upload.limits'])
    ->prefix('admin')
    ->name('admin.')
    ->group(function () {
        // Маршрут для страницы выдачи семян игрокам (должен быть перед ресурсным маршрутом)
        Route::get('/farming-seeds/give', [FarmingSeedController::class, 'showGiveForm'])
            ->name('farming-seeds.give');

        // Маршрут для страницы управления семенами игроков
        Route::get('/farming-seeds/player-seeds', [FarmingSeedController::class, 'playerSeeds'])
            ->name('farming-seeds.player-seeds');

        // Маршрут для редактирования семени игрока
        Route::get('/farming-seeds/player-seeds/{id}/edit', [FarmingSeedController::class, 'editPlayerSeed'])
            ->name('farming-seeds.player-seeds.edit');

        // Маршрут для обновления семени игрока
        Route::put('/farming-seeds/player-seeds/{id}', [FarmingSeedController::class, 'updatePlayerSeed'])
            ->name('farming-seeds.player-seeds.update');

        // Маршрут для удаления семени игрока
        Route::delete('/farming-seeds/player-seeds/{id}', [FarmingSeedController::class, 'destroyPlayerSeed'])
            ->name('farming-seeds.player-seeds.destroy');

        // Ресурсные маршруты для семян
        Route::resource('farming-seeds', FarmingSeedController::class);

        // Маршрут для выдачи семени игроку
        Route::post('/farming-seeds/give-to-player', [FarmingSeedController::class, 'giveToPlayer'])
            ->name('farming-seeds.give-to-player');

        // Ресурсные маршруты для урожая
        Route::resource('harvests', HarvestController::class);

        // Маршрут для выдачи урожая игроку
        Route::post('/harvests/give-to-player', [HarvestController::class, 'giveToPlayer'])
            ->name('harvests.give-to-player');

        // Маршруты для управления шансами выпадения урожая
        Route::get('/harvest-chances', [HarvestChanceController::class, 'index'])
            ->name('harvest-chances.index');
        Route::get('/harvest-chances/{seedId}/edit', [HarvestChanceController::class, 'edit'])
            ->name('harvest-chances.edit');
        Route::put('/harvest-chances/{seedId}', [HarvestChanceController::class, 'update'])
            ->name('harvest-chances.update');

        // Маршрут для получения списка иконок
        Route::get('/api/icons/{type}', [App\Http\Controllers\Admin\IconController::class, 'getIcons'])
            ->name('api.icons');

        // Маршруты для настройки параметров слияния семян
        Route::get('/seed-upgrade-settings', [App\Http\Controllers\Admin\SeedUpgradeSettingsController::class, 'index'])
            ->name('seed-upgrade-settings.index');
        Route::put('/seed-upgrade-settings', [App\Http\Controllers\Admin\SeedUpgradeSettingsController::class, 'update'])
            ->name('seed-upgrade-settings.update');
    });

// Маршруты для системы обелисков (игровая часть)
Route::prefix('obelisk')->name('obelisk.')->middleware(['auth', 'user.activity', 'handle.ActiveEffects', 'check.prologue'])->group(function () {
    // Активация обелиска (постановка метки)
    Route::post('/activate', [App\Http\Controllers\ObeliskController::class, 'activate'])->name('activate');

    // Получение информации об обелиске в текущей локации
    Route::get('/info', [App\Http\Controllers\ObeliskController::class, 'info'])->name('info');

    // Деактивация метки обелиска
    Route::post('/deactivate', [App\Http\Controllers\ObeliskController::class, 'deactivate'])->name('deactivate');
});

// Тестовый маршрут для селектора изображений
Route::get('/test-image-selector', function () {
    return view('test-image-selector');
})->name('test.image.selector');

// Подключение маршрутов для системы групп
require __DIR__ . '/party.php';

// Подключение маршрутов для системы подземелий
require __DIR__ . '/dungeons.php';
