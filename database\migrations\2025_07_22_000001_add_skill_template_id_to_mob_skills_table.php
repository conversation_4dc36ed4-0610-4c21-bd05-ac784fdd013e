<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Добавление поля skill_template_id в таблицу mob_skills
     * для связи с шаблонами скиллов
     */
    public function up(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Добавляем поле для связи с шаблоном скилла
            if (!Schema::hasColumn('mob_skills', 'skill_template_id')) {
                $table->foreignId('skill_template_id')->nullable()->after('skill_id')
                    ->constrained('mob_skill_templates')->onDelete('cascade')
                    ->comment('ID шаблона скилла');
                
                // Добавляем индекс для оптимизации запросов
                $table->index(['skill_template_id'], 'idx_mob_skills_template');
            }
            
            // Добавляем поля для кулдауна
            if (!Schema::hasColumn('mob_skills', 'cooldown_ends_at')) {
                $table->timestamp('cooldown_ends_at')->nullable()->after('cooldown_remaining')
                    ->comment('Время окончания кулдауна');
            }
            
            // Добавляем поле для последнего использования
            if (!Schema::hasColumn('mob_skills', 'last_used_at')) {
                $table->timestamp('last_used_at')->nullable()->after('cooldown_ends_at')
                    ->comment('Время последнего использования скилла');
            }
        });
    }

    /**
     * Откат миграции
     */
    public function down(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Удаляем добавленные поля
            if (Schema::hasColumn('mob_skills', 'skill_template_id')) {
                $table->dropForeign(['skill_template_id']);
                $table->dropColumn('skill_template_id');
            }
            
            if (Schema::hasColumn('mob_skills', 'cooldown_ends_at')) {
                $table->dropColumn('cooldown_ends_at');
            }
            
            if (Schema::hasColumn('mob_skills', 'last_used_at')) {
                $table->dropColumn('last_used_at');
            }
        });
    }
};
