<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MobSkill extends Model
{
    protected $table = 'mob_skills';

    protected $fillable = [
        'mob_id',
        'skill_id',
        'skill_template_id',
        'chance',
        'cooldown_remaining',
        'cooldown_ends_at',
        'last_used_at'
    ];

    protected $casts = [
        'cooldown_ends_at' => 'datetime',
        'last_used_at' => 'datetime',
        'chance' => 'integer',
        'cooldown_remaining' => 'integer'
    ];

    /**
     * Связь с мобом
     */
    public function mob(): BelongsTo
    {
        return $this->belongsTo(Mob::class, 'mob_id');
    }

    /**
     * Связь с умением
     */
    public function skill(): BelongsTo
    {
        return $this->belongsTo(Skill::class, 'skill_id');
    }

    /**
     * Связь с шаблоном скилла
     */
    public function skillTemplate(): BelongsTo
    {
        return $this->belongsTo(MobSkillTemplate::class, 'skill_template_id');
    }

    /**
     * Проверить, доступен ли скилл для использования (не на кулдауне)
     */
    public function isAvailable(): bool
    {
        if (!$this->cooldown_ends_at) {
            return true;
        }

        return now()->gte($this->cooldown_ends_at);
    }

    /**
     * Установить кулдаун для скилла
     */
    public function setCooldown(int $seconds): void
    {
        $this->update([
            'cooldown_ends_at' => now()->addSeconds($seconds),
            'last_used_at' => now()
        ]);
    }

    /**
     * Получить оставшееся время кулдауна в секундах
     */
    public function getRemainingCooldown(): int
    {
        if (!$this->cooldown_ends_at || now()->gte($this->cooldown_ends_at)) {
            return 0;
        }

        return now()->diffInSeconds($this->cooldown_ends_at);
    }

    /**
     * Скоуп для доступных скиллов (не на кулдауне)
     */
    public function scopeAvailable($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('cooldown_ends_at')
                ->orWhere('cooldown_ends_at', '<=', now());
        });
    }

    /**
     * Скоуп для скиллов на кулдауне
     */
    public function scopeOnCooldown($query)
    {
        return $query->where('cooldown_ends_at', '>', now());
    }
}
